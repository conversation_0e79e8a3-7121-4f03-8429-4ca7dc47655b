User = COLLECTION 'chome','user'
Auth = COLLECTION 'chome','login'
UsersExtra = COLLECTION 'chome','user_extra'
ChatDb = COLLECTION 'chome', 'chat'

helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
debugHelper = INCLUDE 'lib.debug'
libUser = INCLUDE 'libapp.user'
libPropertyImage = INCLUDE 'libapp.propertyImage'
libAppVer = INCLUDE 'libapp.appVer'

debug = debugHelper.getDebugger()
# UserProfile = COLLECTION 'chome','user_profile'
SysData = COLLECTION 'chome','sysdata'
checkTextContent = DEF 'checkTextContent'
UserModel = MODEL 'User'
LimiterModel = MODEL 'Limiter'
# config = CONFIG()

ModerationModel = MODEL 'Moderation'
ProvAndCity = MODEL 'ProvAndCity'
# updateUser = DEF 'updateUser'
# getHasNewMsgs = DEF 'getHasNewMsgs'
verify_roles_list = DEF 'verify_roles_list'
{respError} = INCLUDE 'libapp.responseHelper'
libAppVer = INCLUDE 'libapp.appVer'
PushNotifyModel = MODEL 'PushNotify'

MSG_STRINGS.def {
  EMAIL_CHANGED: 'Email changed successfully'
}

getAppUpgradeSettingDB = (cb) ->
  SysData.findOne {_id:'app_upgrade'},(err, ret)->
    if err
      debug.error 'getAppUpgradeSettingDB', err
      return cb err,null
    cb null,ret or {ios:{},android:{}}
    # ret: {_id:'app_upgrade',ios:{data},android:{data}}

initAppUpgradeSetting = (cb) ->
  getAppUpgradeSettingDB (err,ret) ->
    throw err if err
    libAppVer.setgAppUpgradeSettingCache(ret) if ret
    cb()

APPUPGRADE_SETTING_READY =  '_get_appUpgrade_setting'
PLSWAIT APPUPGRADE_SETTING_READY
initAppUpgradeSetting ->
  PROVIDE APPUPGRADE_SETTING_READY



# '/1.5/settings'
APP '1.5/settings'

edit_user_profile_templates = {
  # account info
  ainf : [
    {tp:'email',   fld:'eml', k:'Email', star:1}
    {tp:'phone',  fld:'mbl', k:'Cell', disabled:1, star:1}
    {tp:'text',    fld:'rid', k:'Realtor ID', disabled:1}
    {tp:'btn',  fld:'unfollow',  k:'Following Agent',delete:1}
  ]

  #personal info
  pinf : [
    {tp:'avt',      fld:'avt' , k:'Photo', star:1}
    {tp:'number',   fld:'mblalt', k:'Alternative Cell', class:'col-6'}
    {tp:'text',     fld:'nm',   k:'Nick Name',      ph:''}
    # {tp:'text',     fld:'fn',   k:'First Name',     ph:'', star:1}
    # {tp:'text',     fld:'ln',   k:'Last Name',      ph:'', star:1} #Recommend Fill In
    {tp:'text',     fld:'nm_en',   k:'English Name',     ph:'', star:1}
    {tp:'text',     fld:'nm_zh',   k:'Chinese Name',     ph:'', star:1}
    {tp:'cbool3',   fld:'sex',  k:'Gender', v:['None','Male','Female']}
    {tp:'csel3',   fld:'splang',  k:'Speaking Languages', v:['Mandarin','Cantonese','English'], star:1}
    {tp:'text',     fld:'sgn',  k:'What\'s Up'}
    {tp:'textbox',  fld:'itr',  k:'Introduction', ph:'Introduction About Yourself', star:1}
    #text-id
  ]
  # company info
  cpny : [
    {tp:'text',     fld:'cpny_en',      k:'Company Name (English)', star:1}
    {tp:'text',     fld:'cpny_zh',      k:'Company Name (Chinese)', star:1}
    {tp:'text',     fld:'cpny_pstn', k:'Position', ph:'Like Sales Rep, Broker etc', star:1}
    {tp:'text',   fld:'tel',       k:'Tel'}
    {tp:'text',   fld:'fax',       k:'Fax'}
    {tp:'text',     fld:'cpny_wb',       k:'Website', ph:'Like www.realmaster.ca'}
    {tp:'textarea', fld:'addr',      k:'Address', ph:'Company Address'}
  ]
  #wechat
  wx : [
    {tp:'text',    fld:'wx',        k:'ID', ph:'ID', star:1}
    {tp:'qrcd',    fld:'qrcd',      k:'QR-Code',    url:'/1.5/settings/editUserQRCode?d=/1.5/settings/editProfile', star:1} #, ph:'Upload QR-Code Img'
    {tp:'text',    fld:'wxgrp',     k:'Official Account ID', ph:'', class:"col-6"}
    {tp:'text',    fld:'wxgrpnm',   k:'Official Acct Name', ph:'', class:"col-6"}
    {tp:'qrcd',    fld:'grpqrcd', k:'OA QR-Code', url:'/1.5/settings/editUserQRCode?fld=grpqrcd&d=/1.5/settings/editProfile' }
   ]
  #rent amenities
  smedia : [
    {tp:'text',    fld:'web',    k:'Personal Website', ph:'Like www.realmaster.ca'}
    {tp:'number',  fld:'qq',     k:'QQ',       ph:''}
    {tp:'text',    fld:'wurl',   k:'Weibo',    ph:'URL'}
    {tp:'text',    fld:'fburl',  k:'Facebook', ph:'URL'}
    {tp:'text',    fld:'twturl', k:'Twitter',  ph:'URL'}
  ]
  wordPress: [
    {tp:'text',     fld:'wpHost',      k:'WordPress Host',ph:'Like https://www.realmaster.ca'}
    {tp:'text',     fld:'wpUsername',      k:'WordPress username'}
    {tp:'text',     fld:'wpPwd',      k:'WordPress password'}
    {tp:'text',     fld:'wpSecret',      k:'WordPress secret'}

  ]
  fields : []
}

edit_user_profile_template = {
  ainf:  edit_user_profile_templates.ainf
  pinf:  edit_user_profile_templates.pinf
  cpny:   edit_user_profile_templates.cpny
  wx:  edit_user_profile_templates.wx
  smedia: edit_user_profile_templates.smedia
}

edit_user_other_profile_template = {
  wordPress: edit_user_profile_templates.wordPress
}
init_edit_profile_fields = ()->
  fields = []
  for k, v of edit_user_profile_templates
    for i in v
      if i.fld
        edit_user_profile_templates.fields.push i.fld
  edit_user_profile_templates.fields = edit_user_profile_templates.fields.concat fields
init_edit_profile_fields()

LANGUAGE_LIST = DEF 'LANGUAGE_LIST'
isAllowedLang = (l)->
  ('string' is typeof l) and (l in LANGUAGE_LIST)

# '/1.5/settings/lang'
GET 'lang',(req,resp)-> # TODO: change language, shall use POST
  return resp.redirect '/1.5/index' unless isAllowedLang(lang = req.param 'l')
  d = req.param('d')
  if d
    d = decodeURIComponent d
  else
    d = '/home/<USER>'
  # ,'/1.5/index'
  # word = '?'
  # if d.indexOf('?') > 1
  #   word = '&'
  # d = d+word+'rand='+Math.random()
  # d = '/1.5/index?rand=122'
  # console.log '++++++++',lang,d
  # NOTE: android webview bug with resp.redirect when has #index dont know why not refreshing page if same url
  done = (setSL)->
    req.setLocale lang, ()->
      if setSL
        req.session.set 'sL',lang,->
          return resp.redirect d
      else
        return resp.redirect d
  UserModel.appAuth {req,resp},(user)->
    return done true unless user
    # save to user profile
    UserModel.updateLanguage {req,lang},(err,nUser)->
      if err then console.error err
      done()

# POST 'rlang',(req,resp)->
#   rlang = req.param 'rlang'
#   return resp.send {ok:0} unless (Array.isArray(rlang) and rlang.length <= 3)
#   done = (setSL, rlang)->
#     if setSL
#       req.session.set 'rlang',rlang,->
#         return resp.send {ok:1}
#     else
#       return resp.send {ok:1}
#   UserModel.appAuth {req,resp},(user)->
#     return done(true, rlang) unless user
#     # save to user profile
#     updateUser req,{rlang:rlang},(err,nUser)->
#       if err then console.error err
#       done()

# '/1.5/settings/city'
POST 'city',(req,resp)->
  #return resp.redirect '/1.5/index' unless isAllowedLang(lang = req.param 'l')
  city = req.param 'city'
  error = (err)->
    resp.send {ok:0, e:err}
  return error('No city specified') unless city.o
  ncity = {
    city: city.o
    prov: ProvAndCity.getProvAbbrName city.p
    lat:  city.lat
    lng:  city.lng
  }
  done = ->
    req.session.set 'city', ncity, ->
      return resp.send {ok:1, city:city}
      # return resp.redirect d
  UserModel.appAuth {req,resp},(user)->
    return done() unless user
    UserModel.updateCity {req,newCity:ncity},(err,nUser)->
      if err then console.error err
      done()


# /1.5/settings
dataMethods = DEF 'dataMethods'
appVersion = DEF 'appVersion'

formatUserRoles = (req, roles)->
  ret = {l:[], r:null}
  roles ?= []
  roles = roles.slice()
  for r in verify_roles_list
    if (idx = roles.indexOf(r)) > -1
      v = r.toUpperCase()
      if r is 'realtor'
        v = 'AGENT'
      ret.r = {k:r, v:req._ab(v)}
      roles.splice(idx,1)
  for i in roles
    if i[0] isnt '_'
      ret.l.push {k:i, v:req._ab(i.toUpperCase())}
  if not ret.r
    ret.r = {k:'visitor',v:req._ab('visitor'.toUpperCase())}
  ret

# TODO: sessionUser dup with this
dataMethods.userInfo = (req, user)->
  req.setupL10n() unless req._ab and req._
  unless user
    return {}
  nuser = {locale:user.locale}
  nuser.roles = formatUserRoles(req, user.roles)
  for i in ['avt','nm','sgn','mbl']
    nuser[i] = user[i]
  nuser.eml = UserModel.getEmail(user)
  nuser.fnm = libUser.fullNameOrNickname(req.locale(),user)
  nuser.avt = libPropertyImage.replaceRM2REImagePath avt if avt = nuser.avt
  if user.fn and user.ln
    nuser.nm = user.fn + ' ' + user.ln
  else
    {fn,ln} = libUser.getFirstLastName nuser.fnm
    nuser.fn = fn
    nuser.ln = ln
  nuser

dataMethods.coreVer = (req)->
  return appVersion req

APP_VER = DEF 'APP_VER'
dataMethods.appVer = appVer = (req)->
  # curVer = req.getAppVer()#'5.1.1'
  return APP_VER + (if req.isChinaMode() then 'c' else '') + (if req.isDeveloperMode() then "d" else '') + (if (dm = req.hostname.split('.')[0])?.length < 4 then dm else '') + (if req.getProtocol() is 'https' then 's' else 'h')


# /1.5/settings
GET (req,resp)->
  unless req.getDevType() is 'app'
    return resp.redirect "/adPage/needAPP"
  UserModel.appAuth {req,resp,userfields:['avt','nm','sgn']},(user)->
    # resp.noCache()
    cfg = {noAngular:1, noRatchetJs:1, noUserModal:1}
    ctx =
      title: "RealMaster Settings"
      appVer: appVer req
      page:'me'
      isChinaIP:req.isChinaIP()
      appmode:req.cookies[libUser.APPMODE]
      lang:req.locale()
      os: if req.isIOSDevice() then 'ios' else 'android'
    libAppVer.getAppUpgradeSetting req, {os:ctx.os}, (setting) ->
      ctx.appUpgrade = setting
      resp.ckup 'settings/index',ctx, '_', cfg

translate_flds = (req, orig_tpl)->
  # v is object array
  tpl = {}
  for k, v of orig_tpl
    tpl[k] = []
    for i in v
      ic = {}
      ic = helpers.shallowExtendObject ic, i
      ic.k = req.l10n i.k,'profile'
      # ic.k = 'X' + i.k
      tpl[k].push ic
      if i.v
        ic.v = []
        ic.dbv = []
        for translateK in i.v
          # ic.v.push   'XX' +  translateK
          ic.v.push   req.l10n  translateK
          ic.dbv.push translateK
  return tpl

# '/1.5/settings/editProfile'
GET 'editOtherProfile',(req,resp)->
  # console.log req._
  # console.log req._ab
  d = req.param('d')
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    resp.noCache()
    # console.log req
    template = translate_flds(req , edit_user_other_profile_template)
    isforumAdmin = req.isAllowed 'forumAdmin', user
    # console.log 'isforumAdmin' + isforumAdmin
    verified = {}
    resp.ckup 'editProfileNew',{title:"Edit Other Profile", verified,template, d, isforumAdmin, tp:'other'}

# '/1.5/settings/editProfile'
GET 'editProfile',(req,resp)->
  # console.log req._
  # console.log req._ab
  d = req.param('d')
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['flwng','stars']},(user)->
    resp.noCache()
    # console.log req
    template = translate_flds(req , edit_user_profile_template)
    isforumAdmin = req.isAllowed 'forumAdmin', user
    transRealtor = req.l10n('Realtor')
    # console.log 'isforumAdmin ' + isforumAdmin
    verified = {emlV:false,mblV:false}
    try
      verified = await UserModel.checkVerify user._id
    catch err
      debug.error err
    dataMethods.userFollowedRltr req,user,(err,flwng)->
      for t in template.ainf
        if t.fld is 'unfollow'
          if flwng?._id
            flwng.nm = libUser.fullNameOrNickname(req.locale(),flwng)
            t = Object.assign t,flwng
          else
            template.ainf = template.ainf.slice(0,-1)
          break
      resp.ckup 'editProfileNew',{title:"Edit Profile", template, d, verified,isforumAdmin,profileStars:user.stars}

# '/1.5/settings/editUserProfileImg'
GET 'editUserProfileImg',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['avt']},(user)->
    resp.noCache()
    d = req.param('d') or '/1.5/settings?reload=true'
    user.avt = libPropertyImage.replaceRM2REImagePath avt if avt = user.avt
    # 预先获取用户头像信息传递给 VIEW
    userAvatar = user?.avt
    resp.ckup 'editUserProfileImg',{title:"Edit Profile",user, d, userAvatar}

# '/1.5/settings/editUserQRCode'
GET 'editUserQRCode',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['qrcd','grpqrcd']},(user)->
    resp.noCache()
    fld = req.param 'fld'
    d = req.param('d') or '/1.5/settings?reload=true'
    # console.log fld
    switch fld
      when 'grpqrcd'
        fld = 'grpqrcd'
        titl = 'Edit Official Account QR-Code'
      else
        fld = 'qrcd'
        titl = 'Edit Wechat QR-Code'
    user[fld] = libPropertyImage.replaceRM2REImagePath(user[fld]) if user[fld]
    # 预先获取用户二维码信息传递给 VIEW
    userQRCode = user?[fld]
    resp.ckup 'editUserQRCode',{title:"Upload QR Code",user:user, fld:fld, titl:req.l10n(titl), d:d, userQRCode}

# TODO: BUG this is a reference to original object.
#        even Object.clone is a shallow clone, sub-object still a reference to original object
# NOTE: factory
notificationOption = UserModel.getNotificationOption()
NOTIFICATION_EMAIL_FIELDS= UserModel.NOTIFICATION_EMAIL_FIELDS
NOTIFICATION_PN_FIELDS= UserModel.NOTIFICATION_PN_FIELDS
NOTIFICATION_FLEIDS = NOTIFICATION_PN_FIELDS.concat NOTIFICATION_EMAIL_FIELDS

# '/1.5/settings/notification'
POST 'notification', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    UserModel.updatePostNotification {id:user._id,body:req.body},(err,nUser)->
      if err then return resp.send {ok:0, err:err.toString()}
      resp.send {ok:1}

diagnosingOpt= UserModel.NOTIFICATION_DIAGNOSING
# '/1.5/settings/notification'
GET 'notification', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['pn','eml']},(user)->
    flds = {}
    isRealtor = req.hasRole 'realtor'
    #default on for realtor
    pn = if user.pn then 1 else 0
    fields = {}
    for fld in NOTIFICATION_FLEIDS
      fields[fld] = 1
    UserModel.savedSearchCount {user},(err,ret)->
      if err
        debug.error err
        return resp.ckup 'generalError',{err:MSG_STRINGS.DB_ERROR}
      savedActCount = ret.act
      UserModel.findProfileById user._id, {projection:fields},(err, u)->
        if err
          debug.error err
          return resp.ckup 'generalError',{err:MSG_STRINGS.DB_ERROR}
        u?={}
        for i in notificationOption.mobile.flds
          if i.tag
            i.status = u[i.tag] or 0
            flds[i.tag] = i.status
            if helpers.isNumber(i.number)
              i.number = savedActCount
        for i in notificationOption.email.flds
          if i.tag
            i.status = u[i.tag] or 0 #change orig obj
            flds[i.tag] = i.status
            if helpers.isNumber(i.number)
              i.number = savedActCount
        emailStatus = helpers.deepCopyObject notificationOption.email.emailStatus
        for k,status of emailStatus
          status.word = req.l10n(status.word)
          status.description = req.l10n(status.description)
        try
          emailStatusResult = await UserModel.getEmailStatus user._id
          curStatus = emailStatusResult.status
          showBackdrop = emailStatusResult.showBackdrop
        catch err
          debug.error err
          return resp.ckup 'generalError',{err:err}
        modelInfo={eml:user.eml,diagnosingOpt:diagnosingOpt}
        os = if req.isIOSDevice() then 'ios' else 'android'
        isNewVersion = req.verGTE('6.1.3')
        if not isNewVersion
          #得到appUpgrade需要6.1.3版本。的model的内容。
          modelInfo.appUpgrade = {}
          # TODO: no need os variable
          if os is 'android'
            modelInfo.appUpgrade.downloadLink = '/app-download'
          else
            modelInfo.appUpgrade.downloadLink = iosAppDownloadLink

        modelInfo.lang = req.locale()
        showverify = req.param('showverify') or false
        cfg =
          title:'Notifications'
          noteOpt:flds
          option:notificationOption
          isRealtor:isRealtor
          isDev:req.isAllowed 'devGroup'
          modelInfo:modelInfo
          coreVer:appVersion req
          d: req.param 'd'
          showverify:showverify
          isPopup:req.param 'isPopup'
          curStatus:curStatus
          showBackdrop:showBackdrop
          emailStatus:emailStatus
        resp.ckup 'notification',cfg,'_',{noasync:1}

# '/1.5/settings/receving'
POST 'receving', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless action = req.body?.action
      return resp.send {ok:0,message:req.l10n(MSG_STRINGS.BAD_PARAMETER)}
    if user
      uid = user._id
    if i = req.body?.i
      {err,uid,ts} = UserModel.decodeUserFromParam i
      return respError {clientMsg:req.l10n(MSG_STRINGS.USER_NOT_FOUND), resp} if err or (not uid)
    try
      msg = await UserModel.setEdmNoStatus {action,uid}
    catch err
      return resp.send {ok:0, err:err.toString()}
    resp.send {ok:1,message:req.l10n(msg)}

# '/1.5/settings/diagnosing'
GET 'diagnosing', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['eml']},(user)->
    return resp.ckup 'notification/diagnosing',{eml:user.eml,diagnosingOpt:diagnosingOpt}

# '/1.5/settings/badge'
POST 'badge', (req,resp)->
  # 检查参数是否存在
  b = req.body
  unless b?.pnToken?
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}

  {pnToken, cnt, os} = b
  cnt ?= 0
  os ?= 'ios'
  # 处理pnToken前缀逻辑
  unless pnToken.startsWith('android:') or pnToken.startsWith('ios:')
    pnToken = os + ':' + pnToken

  UserModel.appAuth {req,resp,userfields:['pn']}, (user)->
    try
      pn = user?.pn or pnToken
      await PushNotifyModel.updateBadgeCount(pn, cnt)
    catch err
      debug.warn 'update badge error: ', err
      return respError {clientMsg:err, resp}
    resp.send {ok:1}



redirectLogin = DEF 'redirectLogin'

###
/setting/notificationEml 网页端通知页面
/settings/savedSearch saved search页面
  如果从网页端打开，layout需要添加vue3字段（页面搜索部分为vue2版本，和vue3有冲突，添加字段后不会加在vue2的js，搜索popup页面）

'/1.5/settings/savedSearchModel'
###
GET 'savedSearchModel', (req,resp)->
  cfg = {}
  cfg.noBar = req.param 'nobar' if req.param 'nobar'
  lang = req.param 'lang'
  if req.getDevType() is 'app'
    return UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
      cfg.web = false
      resp.ckup 'settings/savedSearch',cfg
  UserModel.appAuth {req,resp}, (user)->
    i = req.param 'i'
    cfg.web = true
    cfg.noBar = true
    # 用户未登录并且不是从邮件打开的跳转登录页面
    if (not user) and (not i)
      return redirectLogin \
        {resp, state:'/setting/notificationEml',lang,isWeb:true}
    # 用户已经登录打开saved search页面
    if user
      return resp.ckup \
        'settings/savedSearch',cfg,'layout',{angular:1,ratchet:1,vue3:1}
    # edm邮件打开链接，验证后打开saved search页面
    {err,uid,ts} = UserModel.decodeUserFromParam i
    if err or (helpers.dayDiff(ts, new Date()) >1)
      debug.error err if err
      return redirectLogin \
        {resp, state:'/setting/notificationEml',lang,isWeb:true}
    UserModel.getEdmSettingsByUserId uid,(err,user)->
      if err or (not user)
        debug.error err if err
        return redirectLogin \
          {resp,state:'/setting/notificationEml',lang,isWeb:true}
      resp.ckup 'settings/savedSearch',cfg,'layout',\
        {angular:1,ratchet:1,vue3:1}

# # NOTE: deprecated, using settings/savedSearch
# VIEW 'settingSavedSearchModel',->
#   div id:'vueBody',->
#     text """<setting-saved-search-model></setting-saved-search-model>"""
#   coffeejs {vars:{
#     web:@web,
#     noBar:@noBar
#     showCancel:@showCancel
#     }
#   }, ->
#     null
#   css '/css/apps/appMapSearch.css'
#   js '/js/entry/commons.js'
#   js '/js/objectAssignDeep.min.js'
#   js '/js/language/changeLanguage.min.js'
#   js '/js/entry/settingSavedSearchModel.js'
# '/1.5/settings/subscription'
POST 'subscription', (req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user?._id
      return resp.send {ok:0}
    UserModel.updateSubscription {isExuser:req.param('exuser'),body:req.body,id:user._id},(err,nUser)->
      if err then return resp.send {ok:0, err:err.toString()}
      resp.send {ok:1}
# '/1.5/settings/editProfile'
POST 'editProfile',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['noFltr']},(user)->
    findError = (err)->
      resp.send {message:req.l10n( err.toString() ),success:false}
    unless body = req.body
      return findError 'No Data'
    # wecard id check in update
    checkContentObj =
      l10n:(a,b)->req.l10n a,b
      user:user
      collection:'user'
      content: [body.itr,body.nm,body.nm_zh,body.nm_en]
      bypass:['ad']
    checkTextContent checkContentObj,(err, ret) ->
      if err
        console.error err
        return resp.send {success:false, message:err.toString()}
      return resp.send {success:false, message:ret.msg} if ret?.block
      UserModel.updateProfile {req,user,body},(err,user)->
        if err then return findError err
        profileStars = user?.stars or 0
        resp.send {success:true,message:req.l10n('Saved'),profileStars}

# '/1.5/settings/editOtherProfile'
POST 'editOtherProfile',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    findError = (err)-> resp.send {message:req.l10n( err.toString() ),success:false}
    unless body = req.body
      return findError 'No Data'
    UserModel.editOtherProfile {user,body}, (err,user)->
      if err then return findError err
      resp.send {success:true,message:req.l10n('Saved')}

POST 'changeSelfEmail',(req,resp)->
  UserModel.appAuth {req,resp,userfields:['eml']},(user)->
    unless (user?.eml and (b = req.body).neml)
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    neml = b.neml.toLowerCase()
    unless helpers.isEmail b.neml
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    # 先核对验证码，再更换邮箱
    UserModel.verifyEmailVCode {user,body:b,neml},(err,ret)->
      return respError {clientMsg:err, resp} if err
      if ret is 'Successfully Verified' or ret is 'Already Verified'
        ret = MSG_STRINGS.EMAIL_CHANGED
      UserModel.afterChangeEmailOperationsNew {eml:user.eml,neml,ueml:user.eml,deleteThirdParty:1,uid:user._id},(err,ret)->
        nuser = ret?.nuser
        return respError {clientMsg:err, resp} if err
        # update user session
        try
          suser = await UserModel.getFromSession {req, userfields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId']}
        catch err
          return respError {clientMsg:err, resp}
        if suser
          suser.eml = b.neml
          delete suser.wxuid
          delete suser.pic
          delete suser.wxavt
          delete suser.googleId
          delete suser.facebookId
          delete suser.appleId
          req.session.set 'user',suser,->
            return resp.send {ok:1,msg:req.l10n(ret)}
        else
          return resp.send {ok:1,msg:req.l10n(ret)}

POST 'deleteUser',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['eml']},(user)->
    return respError {clientMsg:MSG_STRINGS.NO_USER, resp} if not user
    LimiterModel.isOverLimit {type:LimiterModel.REMOVE_USER,key:req.remoteIP()},(err,isUpperLimit)->
      return respError {clientMsg:err, resp} if err
      if isUpperLimit
        err = req.l10n 'The maximum number of users deleted today has been reached'
        return respError {clientMsg:err, resp}
      UserModel.appLogout req,(err)->
        debug.debug err if err
        UserModel.deleteUser {eml:user.eml},(err)->
          return respError {clientMsg:err, resp} if err
          # TODO: clear user session/cookie
          resp.send {ok:1}

POST 'unfollow',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:MSG_STRINGS.NO_USER, resp} if not user
    UserModel.deleteFollowing req,(err)->
      return respError {clientMsg:err, resp} if err
      resp.send {ok:1,message:req.l10n('Unfollowed')}

VIEW 'editUserProfileImg',->
  css '/css/loading-bar.css'
  css '/css/ng-img-crop.css'
  js '/js/loading-bar.min.js'
  js '/js/ng-img-crop.js?v=1.5.1'
  div ngApp:'appSettings',ngController:'ctrlSetting',->

    header class:"bar bar-nav", ->
      a class: 'icon fa fa-back pull-left ', href: '#',onclick:'goBack(event)',dataIgnore:'push'
      a class: 'pull-right ',ngClick:'save();', style:'margin-right: 10px;padding: 11px 0; position: relative; font-size: 16px; z-index: 20;',->
        text _ 'Upload'
      h1 class:"title", ->
        text _ "Edit Profile Image"
    div class:"content", style:'margin:10px;text-align:center;',->
      div class:"content-padded ", style:'overflow: hidden;',->
        div class:"cropArea",style:' background: #E4E4E4;
          overflow: hidden;
          width:100%;
          height:300px;',->
          text '<img-crop image="myImage"  result-image="myCroppedImage" area-type="circle" area-min-size="40" area-size="areaSize" result-image-format="image/jpeg" crop-result-image-size="myCroppedImageSize" result-image-size="300"></img-crop>'

        #img id: '', class: '', style:'height:300px;width:300px;',src: 'http://placehold.it/420x420'
        div class:'btn btn-block btn-long btn-positive img-upload',style:'margin-top:30px;',->
          label for:'fileInput' ,-> text _ 'Select Picture'
          input id:'fileInput', type:'file',accept:"image/*",style:'opacity:0;overflow: hidden ;display:block;margin-top: -26px;'
        div ->
          div style:'display: inline-block; width:12%; ',->
            button class:'fa fa-rotate-left  btn', ngClick:'rotateImage("left");',style:'margin-top: -69px; margin-right: -38px;'
          div style:'display: inline-block; width:76%',->
            img style:"width:170px;height:170px", ngSrc:"{{myCroppedImage}}"
          div style:'display: inline-block; width:12%; ',->
            button class:'fa fa-rotate-right  btn', ngClick:'rotateImage("right");',style:'margin-top: -69px; margin-left: -38px;'
        if avator = @userAvatar
          input id:'avator',type:'hidden',value:avator
        div style:'display:none',->
          canvas id:'resultImgCanvas',height:"300",width:"300"
  coffeejs {vars:{d:@d, msgTooBig:_('Big file size, may take longer to load. Continue?'),msgContinue:_('Continue'),msgCancel:_('Cancel')}},->
    null
  js '/js/editUserProfileImg.min.js'

VIEW 'editUserQRCode',->
  css '/css/loading-bar.css'
  css '/css/ng-img-crop.css'
  js '/js/loading-bar.min.js'
  js '/js/ng-img-crop.js?v=1.5.1'
  div ngApp:'appSettings',ngController:'ctrlSetting',->

    header class:"bar bar-nav", ->
      a class: 'icon fa fa-back pull-left ', href: '#',onclick:'goBack(event)',dataIgnore:'push'
      a class: 'pull-right ',ngClick:'save();', style:'margin-right: 10px;padding: 11px 0; position: relative; font-size: 16px; z-index: 20;',->
        text _ 'Upload'
      h1 class:"title", ->
        text @titl or _ "Edit Profile Image"
    div class:"content", style:'margin:10px;text-align:center;',->
      div class:"content-padded ", style:'overflow: hidden;',->
        div class:"cropArea",style:' background: #E4E4E4;
          overflow: hidden;
          width:100%;
          height:300px;',->
          text '<img-crop image="myImage"  result-image="myCroppedImage" area-type="square" area-min-size="80" area-size="areaSize" result-image-format="image/jpeg" crop-result-image-size="myCroppedImageSize" result-image-size="300"></img-crop>'

        #img id: '', class: '', style:'height:300px;width:300px;',src: 'http://placehold.it/420x420'
        div class:'btn btn-block btn-long btn-positive img-upload',style:'margin-top:30px;',->
          label for:'fileInput' ,-> text _ 'Select Picture'
          input id:'fileInput', type:'file',accept:"image/*",style:'opacity:0;overflow: hidden ;display:block;margin-top: -26px;'
        div ->
          img ngSrc:"{{myCroppedImage}}"
        if qrcode = @userQRCode
          input id:'qrcode',type:'hidden',value:qrcode

  coffeejs {vars:{d:@d, fld:@fld, msgTooBig:_('Big file size, may take longer to load. Continue?'),msgContinue:_('Continue'),msgCancel:_('Cancel')}},->
    null
  js '/js/editUserQRCode.min.js'

# VIEW 'settingsNew', ->
  # coffeejs {vars:{
    # user:@user,
    # lang:@req.locale(),
    # oldVer:  false,
    # isApp: @req.getDevType() is 'app',
    # actPos:40,
    # hasNewMsg: @has,
    # coreVer:@req.cookies['apsv'],
    # appVer:@appVer or '4.0'
    # }}, ->
    # null
  # div id:'vueBody',->
  #   text """<app-settings></app-settings>"""
  
  # text "<script src='/js/entry/commons.js' async></script>"
  # text "<script src='/js/language/changeLanguage.min.js' async></script>"
  # text "<script src='/js/entry/appSettings.js' async defer></script>"
  # js '/js/entry/commons.js'
  # js '/js/entry/appSettings.js'

###
VIEW 'settings',->
    style '''
    .content, .table-view-cell.split{
      background: #efefef;
    }
    .table-view .table-view-cell.headingBar{
      padding-right: 15px;
      margin: 0;
      border-bottom: 0.5px solid #EFEFEF;
      border-bottom-width: thin;
    }
    .table-view-cell.no-bottom-bar:before{
      border-bottom: none;
    }
    .table-view-cell.split{
      height:20px;
      padding: 0;
    }
    .table-view-cell:before{
      border-bottom:none;
    }
    #userProfile .table-view-cell a{
      border-bottom: 0.5px solid #efefef;
      border-bottom-width: thin;
    }
    .img-upload{
      display: inline-block;
      width: 80px;
      vertical-align: top;
      padding-top: 10px;
    }
    .icon{
      font-size: 18px;
    }
    '''
    text ckup 'busy-icon'
    header class: 'bar bar-nav', ->
      # text ckup 'headerbarUserModalBtn'
      h1 class: 'title',-> _ 'User Settings'
    text ckup 'bottomNav',{actPos:40}

    div id: '', class: 'content', ->
      div id: 'userProfile',->
        ul class: 'table-view', ->
          li class: 'table-view-cell media headingBar', ->
            div class:'img-upload', ->
              #label for:'file-input',->
              a href:'/1.5/settings/editUserProfileImg',dataIgnore:'push', style:' font-size: 13px;',->
                div style:'  padding-left: 5px; height: 60px;',->
                  img id: 'userProfileImg', class: 'media-object pull-left', style:'height:60px;width:60px;border-radius:30px;',src: @user.avt or '/img/icon_nophoto.png'
                if not @user.avt
                  div ->
                    text _ "Click To Edit"
            div class: 'media-body', style:'display: inline-block; width:calc(100% - 80px);',->
              h4 -> #div style:"font-weight: bold;    font-size: 18px; margin-bottom: 10px; line-height: 1;"
                text @user?.nm or _ 'Your Nickname Here'
              p -> @user?.sgn or _ 'Your Signature Here'
              if @user?.roles and Array.isArray @user.roles
                for r in @user.roles
                  if r?.charAt(0) isnt '_'
                    div class:'',style:'padding-top:8px; margin-left: -4px;',->
                      img src:"/img/#{r}.png", style:'height:14px;width:14px;margin:0 5px;'
                      text _ab r.toUpperCase()
              else
                div class:'',style:'padding-top:5px;margin-left: -3px;',->
                  img src:"/img/visitor.png", style:'height:14px;width:14px;0 5px;'
                  text _ "Visitor"
          li class: 'table-view-cell media', ->
            a class: '', href: '/chat/list', ->
              classParam = 'media-object pull-left icon fa fa-commenting-o fa-fw'
              if @hasNewMsg
                classParam += ' new'
              span class: classParam
              div class: 'media-body',-> _ 'My Messages','setting'
          li class: 'table-view-cell media split', ->

          li class: 'table-view-cell media', ->
            a class: '', href: '/1.5/settings/editProfile', dataIgnore:'push',->#dataTransition: 'slide-in', ->
              span class: 'media-object pull-left icon fa fa-edit fa-fw'
              div class: 'media-body',-> _ 'Edit Profile','personal'
          li class: 'table-view-cell media', ->
            a class: '', href: '/1.5/user/changePwd', dataIgnore: 'push', ->
              span class: 'media-object pull-left icon fa fa-user-secret fa-fw'
              div class: 'media-body',-> _ 'Change Password'
          if @req.getDevType() is 'app'
            coffeejs ->
              RMSrv.hasWechat (has)->
                if has
                  document.getElementById('bindWechatAuth').style.display = 'block'
              wechatAuth = ->
                RMSrv.wechatAuth()
                document.getElementById('busy-icon').style.display = 'block'
                false
              null
            li class: 'table-view-cell media', id:"bindWechatAuth", style:"display:none;",->
              a class: '', href: 'javascript:void(0)',onclick:"wechatAuth();", dataIgnore: 'push', ->
                span class: 'media-object pull-left icon fa fa-wechat fa-fw'
                div class: 'media-body',-> _ 'Connect Wechat Account'
          # li class: 'table-view-cell media', ->
          #   a class: '', href: 'javascript:connect58();', dataIgnore: 'push', ->
          #     span class: 'media-object pull-left icon fa fa-user-secret fa-fw'
          #     div class: 'media-body',-> _ 'Connect 58.com Account'
          # http://openapi.58.com/oauth2/authorize?client_id=**************&response_type=code&redirect_uri=http://realmaster.com/callback/58
          #!!! dont remove
          li class: 'table-view-cell media no-bottom-bar', ->
            a class: '', href: '/1.5/user/identity', dataIgnore: 'push', ->
              span class: 'media-object pull-left icon fa fa-trophy fa-fw'
              div class: 'media-body',-> _ 'Verification'

          li class: 'table-view-cell media split', ->

          li class: 'table-view-cell media', ->
            a class: '', href: '/1.5/settings/notification', dataIgnore: 'push', ->
              span class: 'media-object pull-left icon fa fa-bell-o fa-fw'
              div class: 'media-body',-> _ 'Notifications','settings'
          li class: 'table-view-cell media no-bottom-bar', ->
            a class: '', href: '/1.5/settings/subscription', dataIgnore: 'push', ->
              span class: 'media-object pull-left icon fa fa-envelope-o fa-fw'
              div class: 'media-body',-> _ 'Email Subscription','settings'

          li class: 'table-view-cell media split', ->

          # everyone can have wecard, but realtor can have their own menu
          li class: 'table-view-cell media', ->
            a class: '',href:'/1.5/wesite?d=/1.5/settings',dataIgnore:'push', -> #/1.5/wecard
              span class: 'media-object pull-left icon fa fa-list-alt fa-fw'
              div class: 'media-body',-> _ 'WeCard'

          # everyone can have wepage, but expires after 2month and has number limit
          # sponsored user or realtor can share
          if (@req.getDevType() is 'app') or (@req.hostname is 'i.realmaster.com')
            li class: 'table-view-cell media', ->
              a class: '',href:'/1.5/wecard/',dataIgnore:'push', -> #/1.5/wecard
                span style:'    font-size: 20px; margin-left: -3px;', class: 'media-object pull-left icon  icon-pages'
                div class: 'media-body',-> _ 'WePage'

          li class: 'table-view-cell media no-bottom-bar', ->
            a class: '',href:'/1.5/tools', dataTransition: 'slide-out',->
              span class: 'media-object pull-left icon fa fa-calculator fa-fw'
              div class: 'media-body',-> _ 'Tools'
###
VIEW 'notification', ->
  div ngApp:'appProfile',ngController:'ctrlNotifications', class:"ng-cloak",->
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', dataIgnore:'push', ngClick:"goBack()"
      h1 class: 'title',-> _ "Notifications"

    div class: 'content', ->
      div class:'loader-wrapper', ngShow:"loading", style:"display: block",->
        div class:"loader", ngShow:"loading", style:"display: block"

      divParam = class:'check-notification', ngShow:'!hasPn'

      div divParam, ->
        div class:'status fa fa-exclamation-circle',->
        div class:'field-name',ngIf:'!hasGoogleService',->
          text _ 'Install Google Play to get listing updates.'
        div class:'field-name',ngIf:'hasGoogleService',->
          text _ 'Turn on notifications to get listing updates.'
          span class:'explain',ngClick:'checkNotification()',ngIf:'canRefresh && refresh',->
            text _ 'Refresh'
          span class:'explain',ngClick:'openSetting()',ngIf:'!(canRefresh && refresh)',->
            text _ 'Notification Settings'

      # show option if android
      div class:'', ->
        text JSON.stringify @formData
        for k,v of @option
          div class:'field-wrapper', ->
            div class:'field-header' , style:'',->
              div class:'field-name',style:'width:63%;', ->
                # h3 class:'',->
                div -> text _ v.title
                div class:'explain',->
                  if k is 'mobile'
                    text _ v.description
                  else
                    text '{{emailStatus.description}}'
                # div class:'' ,ngIf:"'#{v.title}' == 'Email'",\
                #     ngClick:"turnOffAll()",style:'color: #5CB85C;font-weight: bold;font-size: 13px;padding-top:10px;',->
                #   text _ 'TURN OFF ALL'
              div class:"field-input #{k}",ngClass:'emailStatus.action',ngClick:"emailAction(emailStatus.action,'#{k}')", ->
                if k is 'mobile'
                  text _ v.action
                else
                  span ngClass:"emailStatus.cls",ngIf:'emailStatus.action == "blocked"' ,ngStyle:"{color:emailStatus.color}",->
                  span style:'padding-left: 5px;',->
                    text '{{emailStatus.word}}'

            div class:'fields',->
              #mobile pn，没有pn时不能修改。
              if k is 'mobile'
                divParam2 = ngHide:'hasPn'
              if k is 'email'
                divParam2 = ngShow:"showBackdrop"
              div class:'backdrop',divParam2,->
              for n,i in v.flds
                div class:'row',ngIf:"'#{n.agent}' == 'false' || ('#{n.agent}' == 'true' &&  #{@isRealtor}) || ('#{n.devGroup}' == 'true' &&  #{@isDev})",->
                  div class:'field-name', ->
                    label ngClass:'',->
                      span class:'',->
                        text _ n.title
                      span class:'',ngIf:"'#{n.number}' != 'undefined'",style:'display: inline-block;font-size: 12px;line-height: 14px;margin-left: 3px;font-weight: normal;',->
                        span ngIf:"formData['#{n.tag}'] == 1",->
                          text '('+0+')'
                        span ngIf:"formData['#{n.tag}']!= 1",->
                          text '('+n.number+')'
                      div class:'explain',->
                        text _ n.description
                      div ngIf:"'#{n.src}' != 'undefined'",\
                          ngClick:"linkTo('#{n.src}')",style:'color: #5CB85C;font-weight: bold;font-size: 13px;padding-top:10px;',->
                        text _ 'CUSTOMIZE'
                  div class:'field-input', ->
                    div id:"#{n.tag}",class:"toggle pull-right", href:'#' ,ngClass:"{'active' : formData['#{n.tag}'] != 1 }",\
                        ngIf:"('#{n.tag}' != 'undefined')",ngSrc:"#{n.tag}",->
                      div class:"toggle-handle #{k}"
                    # div class:'icon icon-right-nav',ngIf:"'#{n.tag}'=='undefined'",ngClick:"linkTo('#{n.src}')",style:'color: #bbb; font-size: 16px; padding: 10px; padding-right: 0; width: 100%;'

        # # div class:'fields',->
        #   list = [['Newsletter','edmNoNews'],['Property','edmNoProperty'],['Forum','edmNoForum'],['Projects','edmNoProject'],['Stat','edmNoStat'],['Assignment','edmNoAssignment'],['Exclusive','edmNoExlisting'],['Evaluation','edmNoEvaluation']] # edmEvent edmReport
        #   for n in list
        #     div class:'row',->
        #       div class:'field-name', ->
        #         label ngClass:"",->
        #           text _ n[0]
        #       div class:'field-input', ->
        #         div class:'toggle', href:"#" , ngClass:"{'active' : formData['#{n[1]}'] !== 1 }",\
        #             ngClick:"setBool('#{n[1]}', 0)",->
        #           div class:"toggle-handle"
                # a class:'toggle', href:"#" , ngClass:"{'active' : formData['#{n[1]}'] == 1  }",
                #   ngClick:"setBool('#{n[1]}', 1)",-> _ "OFF",'switch'
    text ckup 'flashMessage',{id:'notification',msg:'message'}
    div ngShow:'showDiagnosing',->
      text ckup 'notification/diagnosing',{eml:@modelInfo.eml,diagnosingOpt:@modelInfo.diagnosingOpt}
    if @modelInfo.appUpgrade
      div id:'appUpgradeModel',ngShow:'showAppUpgrade',->
        text ckup 'home/appUpgrade',{appUpgrade:@modelInfo.appUpgrade,lang:@modelInfo.lang}
  #TODO: move to coffee4client
  coffeejs {vars:{
    d:@d,
    noteOpt:@noteOpt,
    option:@option,
    isRealtor:@isRealtor,
    template:@template,
    coreVer:@coreVer,
    modelInfo:@modelInfo,
    showverify:@showverify,
    isPopup:@isPopup,
    curStatus:@curStatus
    showBackdrop:@showBackdrop
    emailStatus:@emailStatus}},->
      null
  js '/js/notification.min.js'
  css '/css/apps/notification.css'

VIEW 'block-text-edit-modal', ->
  div id:"#{@fld or ''}Modal", class:'modal ', style:'z-index:20;', ->
    header class: 'bar bar-nav', ->
      a class: 'icon icon-close pull-right nobusy', onclick:"toggleModal('#{@fld or ''}Modal');", href: 'javascript:void 0'
      h1 class: 'title',-> "#{@titl or ''}"
    div class: 'content', ->
      textarea row:'10', name:"#{@fld or ''}", placeholder:"#{@ph or ''}", style:"resize: none; height:100%; margin-bottom:0; padding: 10px 5px;", ngModel:"formData['#{@fld or ''}']"

VIEW 'editProfileNew',->
  div ngApp:'appProfile',ngController:'ctrlEditProfile', class:'ng-cloak',->

    text ckup 'block-text-edit-modal', {fld:'itr', titl:@req.l10n('Introduction'), ph:@req.l10n('Introduction')}

    header class: 'bar bar-nav', ->
      span class: 'icon fa fa-back pull-left', dataIgnore:'push', ngClick:'goBack()'
      h1 class: 'title',-> _ @title

    div class: 'bar bar-standard bar-footer edit-footer',style:'padding:0;', ->
      div class : 'btn btn-half btn-sharp btn-fill btn-negative', ngClick:'submitForm(userForm.$valid);',->
        span ngShow: '!submitting', ->
          text _ 'Save'
        span ngShow: 'submitting', ->
          text _ 'Submitting'

      div class : 'btn btn-half btn-sharp btn-fill profile-rating',->
        text _ 'Profile Rating'
        div class:'rating',->
          span class:'fa',ngRepeat:"i in [1,2,3,4,5]",ngClass:"{'fa-star' : profileStars>i-1,'fa-star-o' : profileStars<=i-1,'fa-star-half-o' : profileStars==i-0.5} "


    div class:'loader-wrapper', ngShow:"loading", style:"display: block",->
      div class:"loader", ngShow:"loading", style:"display: block"
      # a class: 'icon pull-left fa fa-save',style:'font-size:22px; margin-left:6px; color: black;' , ngClick:'submitForm(userForm.$valid);'
      # span style:'margin-left: 6px;color: black;display: inline-block;margin-top: 11px;',
    text ckup 'flashMessage',{id:'saved',msg:_('Saved')}
    div class: 'content', -> #style:'position: relative;',
      form name:'userForm', novalidate:'', ngSubmit:'submitForm(userForm.$valid)',  ->
        div class:'user-fields', ->
          arr = [['Account Info','ainf',' Recommended fields'], ['Personal Info','pinf'], ['Company Info','cpny'], ['WeChat', 'wx',''], ['Social Media','smedia', ''],['Delete Account','delete','']]
          if @tp=='other'
            arr = [['WordPress Config','wordPress', '']]
          for n in arr#(Recommend if you have)
            if n[1]!='wordPress' || @isforumAdmin
              div class:'field-wrapper', ngHide:'',->
                divParam = class:'fields'
                if n[1] is 'ainf'
                  divParam = class:'fields oneLine'
                  div class:'field-header' , style:'', ngClick:'',->
                    text _ n[0]
                    span class:'placeholder',->
                      text '('
                      span class:'fa fa-star-of-life'
                      text _ n[2]
                      text ')'
                    # if vrole = @req.verifiedRole()
                    #   initStr = 'isVerified = true'
                    #   initStr += '; isRealtor = true' if vrole is 'realtor'
                    #   a class:'fa fa-edit pull-right', href:"/1.5/user/verify?tp=#{vrole}&d=/1.5/settings",ngInit:initStr, style:' color:#5CB85C; vertical-align: middle; margin-right: 0px;'
                else
                  div class:'field-header' ,->#ngShow:"template['#{n[1]}']",
                    text _ n[0]
                    if n[2]
                      span class:'placeholder',->
                        text _ n[2]

                div divParam, ->
                  div class:'row',ngId:'i.fld', ngRepeat:"i in template['#{n[1]}']", ngShow:"template['#{n[1]}']", ngSwitch:"i.tp", ngClass:"{'full-height' : ['avt','itr','addr','qrcd','grpqrcd'].indexOf(i.fld) > -1 ,'block' : ['itr','addr'].indexOf(i.fld) > -1,'unfollow':i.fld === 'unfollow'}", ngHide:"i.fld === 'rid' && !isRealtor", ->

                    div class:"field-name {{i.class}}",  ngClass:"{'full-width' : ['itr','addr','unfollow'].indexOf(i.fld) > -1}",->
                      label ->
                        span -> text "{{i.k}}"
                        span class:"fa fa-star-of-life", ngIf:"i.star"
                      span class:'hide-mbl-button',ngClass:"{'active' : formData.hmbl === true }", ngIf:"i.fld=='mbl'", ngClick:"formData.hmbl=!formData.hmbl",-> text _ "Hide"

                    div class:"field-input {{i.class}}", ngClass:"{'full-width' : ['itr','addr'].indexOf(i.fld) > -1}",->

                      input type:"number", name:"{{i.fld}}", ngSwitchWhen:"number", ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngDisabled:"i.disabled && isVerified"

                      input type:"text",   name:"{{i.fld}}", ngSwitchWhen:"text",   ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngDisabled:"i.disabled && isVerified" #use i.disabled

                      input type:"text",   name:"{{i.fld}}", ngSwitchWhen:"text-id",   ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngMaxlength:'15',ngMinlength:'6',ngPattern:'/^[a-zA-Z0-9\-\_]+$/'

                      if vrole = @req.verifiedRole()
                        initStr = 'isVerified = true'
                        initStr += '; isRealtor = true' if vrole is 'realtor'

                      div style:'width:100%;height:100%;position: absolute;top:0;',ngIf:"i.fld === 'eml'",ngClick:"emlActions()"
                      i ngIf:"i.fld === 'eml'",class:'fa',ngClass:"{'fa-check-circle':verified.emlV == true,'fa-exclamation-circle':verified.emlV == false}"
                      b type:"email",  name:"{{i.fld}}", ngSwitchWhen:"email",  ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngDisabled:"true",style:'color: #888;padding-left:5px;max-width: calc(100% - 40px);display: inline-block;overflow: hidden;text-overflow: ellipsis;',->
                        text "{{verified.eml}}"
                      b ngShow:"i.fld === 'eml'",class:'icon icon-right-nav'
                      i ngIf:"i.fld === 'mbl'",class:'fa',ngClass:"{'fa-check-circle':verified.mblV == true,'fa-exclamation-circle':verified.mblV == false}"
                      b ngIf:"verified.mbl",type:"email",  name:"{{i.fld}}", ngSwitchWhen:"phone",  ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", ngDisabled:"true",style:'color: #888;padding-left:5px;max-width: calc(100% - 40px);display: inline-block;overflow: hidden;text-overflow: ellipsis;',->
                        text "{{verified.mbl}}"
                      div style:'width:100%;height:100%;position: absolute;top:0;',ngIf:"(i.fld === 'mbl' || i.fld === 'rid')&& i.disabled && isVerified",ngClick:"safeJump('/1.5/user/verify?tp=#{vrole}&d=/1.5/settings')"
                      div style:'width:100%;height:100%;position: absolute;top:0;',ngIf:"i.fld === 'mbl'&& ('#{vrole}' == 'null')",ngClick:"openVerifyPopup('p')"
                      b ngShow:"i.fld === 'mbl'",class:'icon icon-right-nav'
                      b ngShow:"i.fld === 'rid' && i.disabled && isVerified",class:'icon icon-right-nav',ngInit:initStr
                      div ngIf:"i.fld === 'id'", ->
                        p class:'error-block', ngShow:"userForm.id.$error.minlength ",style:'padding-top: 7px;',-> #&& !userForm.id.$pristine
                          text _ 'WeCard ID too short, at least 6 chars.'
                        p class:'error-block', ngShow:"userForm.id.$error.maxlength ",style:'padding-top: 7px;',->
                          text _ 'WeCard ID too long, at most 15 chars.'
                        p class:'error-block', ngShow:"userForm.id.$error.pattern ",style:'padding-top: 7px;',->
                          text _ "Only alpha-beta, number and '-' please."


                      span ngSwitchWhen:"cbool3", style:"display: flex;", class:"cbool3",->
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld] === i.dbv[0] }", ngClick:"setBool(i.fld, i.dbv[0])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[0]}}"
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld] === i.dbv[1] }", ngClick:"setBool(i.fld, i.dbv[1])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[1]}}"
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld] === i.dbv[2] }", ngClick:"setBool(i.fld, i.dbv[2])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[2]}}"

                      span ngSwitchWhen:"csel3", style:"display: flex;", class:"cbool3",->
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld].indexOf(i.dbv[0]) > -1 }", ngClick:"setSel(i.fld, i.dbv[0])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[0]}}"
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld].indexOf(i.dbv[1]) > -1 }", ngClick:"setSel(i.fld, i.dbv[1])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[1]}}"
                        a class:'btn btn-default', href:"#" , ngClass:"{'active' : formData[i.fld].indexOf(i.dbv[2]) > -1 }", ngClick:"setSel(i.fld, i.dbv[2])",
                          style:'width:33%; white-space: normal;', -> "{{i.v[2]}}"

                      div ngSwitchWhen:"textarea",->
                        textarea rows:'2', name:"{{i.fld}}", ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", style:"resize: none;"

                      div ngSwitchWhen:"avt", ->
                        img src:'/img/icon_nophoto.png', ngSrc:"{{formData.avt}}", style:"width:80px; height:80px;", class:"", ngClick:"safeJump('/1.5/settings/editUserProfileImg?d=/1.5/settings/editProfile')"
                      div ngSwitchWhen:"qrcd", class:"qrcd", ->
                        a href:"#",  ngClick:"safeJump(i.url)",->
                          img src:'/img/icon_noqrcode.png', style:"width:80px; height:80px;", ngSrc:"{{formData[i.fld]}}"
                          text _ "" #Upload QR-Code Img.
                          # i class:"fa fa-qrcode"

                      div ngSwitchWhen:"textbox", ->
                        textarea rows:'3', name:"{{i.fld}}", ngModel:"formData[i.fld]", placeholder:"{{i.ph}}", style:"resize: none;", ngClick:"toggleModal(i.fld + 'Modal'); ", ngReadonly:"i.fld === 'itr'"

                      div ngSwitchWhen:"btn",class:'flwngAgent', ->
                        span class:'nm',->
                          text '{{i.nm}}'
                        span class:'delbtns btn btn-nooutline',ngIf:'i.clicked',style:'padding-top: 7px;',->
                          span class:'btn btn-negative button', ngClick:'btnClicked(i);', ->
                            text _'Delete'
                          span class:'btn btn-nooutline button', ngClick:'i.clicked = false',->
                            text _'Cancel'
                        span class:'delbtns hasIcon btn btn-nooutline',ngIf:'!i.clicked',->
                          span class:'btn btn-nooutline fa fa-trash',style:'color: #bbb;', ngClick:'i.clicked = true;'

          div style:'background:#fff;padding: 10px 15px 15px 10px;font-size: 15px;overflow:hidden',->
            div ->
              div style:'padding-bottom: 15px;',->
                text _ 'If you do not think you will use Realmaster again and would like your \
                account deleted,  keep in mind that you will not be able to reactivate your account \
                or retrieve any of the content or information you have added.'
              div style:'padding-bottom: 15px;',->
                text _ 'If you would like your account deleted, click "Delete my account".'
            div class:'',->
              span  class:'pull-right btn btn-positive',ngClick:'promptedDeleteProfile()',->
                text _ 'Delete My Account'

    div ngShow:'selectEmailAction',class:'selectModel',->
      div class:'backdrop',ngClick:'selectEmailAction = false'
      ul class:'table-view modal modal-60pc active',->
        li class:'table-view-cell',->
          span class:'',->
            text _ 'Linked Accounts'
          i class:'fa fa-wechat',ngIf:'formData.wxuid || formData.wxAppOID || formData.wxMobOID'
          i class:'fa fa-facebook',ngIf:'formData.facebookId'
          img class:'fa fa-google',ngIf:'formData.googleId',
            src:'https://developers.google.com/identity/images/g-logo.png'
          i class:'fa fa-apple',ngIf:'formData.appleId'
        li class:'table-view-cell',ngClick:"openVerifyPopup('v')",ngIf:'verified.emlV == false',->
          span class:'sprite16-18 sprite16-5-5 margin-right-5'
          span class:'',->
            text _ 'Verify your email'
          span class:'pull-right',->
            span class:'fa fa-exclamation-circle',style:'color:#FFCD00;'
            span style:'padding-left: 5px;',-> text _ 'Unverified'
        li class:'table-view-cell',ngClick:"openVerifyPopup('c')",->
          span class:'sprite16-18 sprite16-5-5 margin-right-5'
          span class:'',->
            text _ 'Change email address'
        div class : 'bar bar-standard bar-footer', ngClick:'selectEmailAction=false',->
          text _ 'Cancel'

  #TODO: move to coffee4client
  coffeejs {
    vars:{
      template:@template,
      d:@d,
      tp:@tp,
      verified:@verified,
      profileStars:@profileStars,
      strDeleteTip: @req.l10n('Are you sure you want to delete your account? \
       After deletion, you can not log in to the APP with the current user account again.'),
      strCancle: @req.l10n('Cancel'),
      strConfirm: @req.l10n('Confirm delete')
    }},->
      null
  js '/js/editProfileNew.min.js'
  css '/css/sprite.min.css'
#LESS
STYLUS 'editProfileNew','''
  input[disabled], input:disabled
    color #888888
    -webkit-text-fill-color:#888888
  .user-fields
    padding-bottom: 40px;
    background-color #efefef
    .field-header
      font-weight: bold;
      font-size: 17px;
      padding: 20px 10px 10px 10px;
      .placeholder
        color: #666;
        font-size: 11px;
        font-weight: normal;
        padding-left: 10px;
        vertical-align: middle;
    .field-header.error
      color #E03131
  .fields .row
    font-size: 15px;
    padding: 10px;
    border-top: 0.5px solid #efefef;
    overflow: hidden;
    background: white;
    display: flex;
    align-items: center;
    .field-name
      label
        margin-bottom 0
        max-width: 100%;
        word-wrap: break-word;
        font-weight bold
        vertical-align: middle;
        display: inline-block;
        span
          vertical-align: middle;
      width: 33%
      position: relative;
      display: flex;
      align-items: center;
    min-height: 35px;
    .field-name.col-6
      width 50%
    .field-name.full-width
      width 100%
    .field-input.col-6
      width 50%
    .field-input
      text-align: right;
      .qrcd a
        color black
        i
          font-size 15px
          color #666
          margin-left 5px
          vertical-align: middle;
      vertical-align: middle
      input,textarea
        min-height: 33px;
        outline: none
        border: 1px none
        border-bottom: 1px none
        padding: 0;
      textarea
        text-align left
      input:focus,textarea:focus
        border-bottom: 1px none
      *
        margin-bottom 0
        border-radius: 0;
        font-size 15px
        text-align right
        vertical-align: middle;
      width 66%
      display inline-block
      > span
        display inline-block
        width 100%
        a
          width 50%
        a:not(:first-child)
          border-left: 1px none
      >span.cbool3
        a
          padding: 6px 1px 7px;
          font-size 13px;
          text-align center

    .field-input.full-width
      width 100%

  .fields .row
    &.full-height
      height: auto;
      text-align left
    &.block
      display:block
  .hide-mbl-button
    padding: 5px;
    font-size: 11px;
    border-radius: 5px;
    border: 0.5px solid #ccc;
    margin-left: 10px;
  .hide-mbl-button.active
    background-color #ccc;
  .bar
    .btn
      padding: 10px 0;
    .profile-rating
      font-size: 12px;
      padding: 7px 15px;
      line-height: 16px;
      text-align: right;
      .rating
        color:#efc439
  .oneLine
    .field-input
      white-space: nowrap;
      position: relative;
      input
        width:90%;
      b
        font-weight: normal;
        color: #bbb;
        font-size: 16px;
        padding: 10px;
        padding-right: 0;
  .selectModel  li
    padding-right:15px;
    text-align: left;
    color: #666;
    border-bottom: 0;
    span
      vertical-align: middle;
      font-size: 15px
  .selectModel .table-view
    background: #fff;
    z-index: 15;
    position: absolute;
    width: 100%;
    bottom: 0;
    margin: 0;
  .fa-wechat {
    color: #30c802;
    font-size: 17px;
    vertical-align: middle;
    margin-left: 5px;
  }
  .fa-facebook {
    color: #2653b4;
    font-size: 17px;
    vertical-align: middle;
    margin-left: 5px;
  }
  .fa-apple {
    font-size: 17px;
    vertical-align: middle;
    margin-left: 5px;
  }
  .fa-google {
    width: 17px;
    height: 17px;
    vertical-align: middle;
    margin-left: 5px;
  }
  .bar-footer {
    font-size: 15px;
    line-height: 44px;
    text-align: center;
  }
  .margin-right-5{
    margin-right: 5px;
  }
  span.fa-star-of-life
    display: inline-block;
    color: #e03131;
    font-size: 10px;
    vertical-align: middle;
  .fa-check-circle
    color: #5CB85C
  .fa-exclamation-circle
    color: #FFCD00
  .flwngAgent
    display: flex;
    justify-content: flex-end;
    .nm
      color: #888;
      padding: 10px;
      padding-right: 0;
      max-width: 85%;
      overflow: hidden;
      text-overflow: ellipsis;
    .delbtns
      padding-right:0;
      .button
        font-size:12px;
        border-radius:3px;
'''

Evaluation = COLLECTION 'chome','evaluation'

# '/1.5/settings/myProperty'
GET 'myProperty', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    Evaluation.findToArray {owner:user._id.toString()},(err, props)->
      fields = ['city', 'cnty','lng','lat','prov','st','st_num','addr','mlsid','bdrms','bthrms','tp','br_plus','gr','tax','sqft','img']
      for prop in props
        arr = []
        for key in fields
          if prop[key]
            arr.push(key+'='+prop[key])
        prop.url = '/1.5/evaluation/evaluatePage.html?back=1&inframe=1&' + arr.join('&')
        if prop.addr
          prop.addr =  prop.addr
        else
          prop.addr = if prop.st_num then prop.st_num+' '+prop.st else prop.st

        prop.address = "#{prop.addr} #{prop.city}, #{prop.prov}"
      resp.ckup 'myProperty',{title:req.l10n("My properties"), props:props}

VIEW 'myProperty', ->
  div ngApp:'appProfile',ngController:'ctrlNotifications', class:'ng-cloak',->
    header class: 'bar bar-nav', ->
      a class: 'icon fa fa-back pull-left', href: '/1.5/settings', dataIgnore:'push', ngClick:"goBack()"
      h1 class: 'title',-> _ 'My properties'

    div class: 'content', ->
      div class:'fields', ->
        for prop in @props
          div class:'row',->
            div class:'field-name', style:"padding: 7px 10px;",->
              a style:'color:#666!important',ngClick:"goEvaluation('#{prop.url}')", ->
                prop.address

  coffeejs {vars:{fld:@fld }},->
    appProf = angular.module 'appProfile',[]
    appProf.controller 'ctrlNotifications',['$scope','$http',($scope,$http)->
      $scope.goEvaluation = (url)->
        document.location.href = url
        return
      ]
    null
iosAppDownloadLink = DEF 'iosAppDownloadLink'
# '/1.5/settings/alertwindow'
GET 'alertwindow', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/index'},(user)->
    return resp.redirect '/1.5/index' unless req.isAllowed 'admin'
    getAppUpgradeSettingDB (err, ret) ->
      unless ret.ios.downloadLink
        #downladLink move to constant.
        ret.ios.downloadLink = iosAppDownloadLink
      # unless ret.android.downloadLink
      #   ret.android.downloadLink = 'https://play.google.com/store/apps/details?id=com.realmaster'
      ctx = {setting:ret}
      ctx.err = err if err
      resp.ckup 'settings/alertWindow', ctx

# '/1.5/settings/alertwindow'
POST 'alertwindow', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/index'},(user)->
    return resp.redirect '/1.5/index' unless req.isAllowed 'admin'
    return resp.redirect '/1.5/settings/alertwindow' unless body = req.body
    setting = {ios:{},android:{}}
    for k,v of body
      [os,key] = k.split('.')
      v = parseInt(v) if key in ['showAlert','forceUpgrade']
      setting[os][key] = v
      setting[os]['feature_zh'] = i18n.JT2FT v if v and (key is 'feature_zh-cn')
    setting.mt = new Date()
    setting._id = 'app_upgrade'
    SysData.updateOne {_id:'app_upgrade'}, {$set:setting}, { upsert: true }, (err)->
      if err
        debug.error 'SysData update app_upgrade', err
        return resp.ckup 'settings/alertWindow', {setting,ok:0,err:err.toString()}
      ctx = {setting, ok:1}
      libAppVer.setgAppUpgradeSettingCache setting
      req.session.set libAppVer.APP_UPGRADE_SESSION_KEY,1 # 1 means force showAlert
      resp.ckup 'settings/alertWindow', ctx

GET 'moderation', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/index'},(user)->
    return resp.redirect '/1.5/index' unless req.isAllowed 'admin'
    page = req.param('page') or 0
    page = parseInt page
    ModerationModel.findList {page}, (err,logs)->
      ctx = {logs,page}
      ctx.err = err if err
      resp.ckup 'settings/moderation', ctx

# '/1.5/settings/user'
# NOTE: app setup添加设置首页默认打开页面feeds/new
# GET 'user',(req,resp)->
#   d = req.param('d')
#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
#     resp.ckup 'settings/appSetting', user

POST 'user',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    findError = (err)-> resp.send {msg:req.l10n( err.toString() ),ok:0}
    unless body = req.body
      return findError 'No Data'
    UserModel.updateSettings {req,user,body}, (err,user)->
      if err then return findError err
      resp.send {ok:1,msg:req.l10n('Saved')}

verifyMap =
  c : 'changeemail'
  v : 'verifyemail'
  p : 'verifyphone'

# 用户验证页面
GET 'verify', (req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/index',userfields:['mbl','eml']},(user)->
    mode = verifyMap[req.param('verify')]
    return resp.redirect '/1.5/index' unless mode
    ctx =
      eml:UserModel.getEmail(user),
      mbl:user.mbl,
      mode:mode
    bodyStyle = 'background-color: transparent;overflow: hidden;'
    resp.ckup 'notification/verifyemail', ctx,'_',{bodyStyle}