config = CONFIG(['followUpBoss','contact','mailEngine','serverBase'])

helpers = INCLUDE 'lib.helpers'
libUser = INCLUDE 'libapp.user'
{CONTACT_REALTOR_VALUES,TITLE_STR} = INCLUDE 'libapp.common'
formInputLib = INCLUDE 'libapp.formInput'
followUpBossLib = INCLUDE 'libapp.followUpBoss'
contactRealtorHelper = INCLUDE 'libapp.contactRealtor'
propertiesHelper = INCLUDE 'libapp.properties'
{getPropShareUrl} = INCLUDE 'libapp.propWeb'
debugHelper = INCLUDE 'lib.debug'
debug = debugHelper.getDebugger()

helpersFunction = INCLUDE 'lib.helpers_function'
{hasChinese} = INCLUDE 'lib.helpers_string'
Form = COLLECTION 'chome','form'
FormContent = COLLECTION 'chome','form_content'
Forminput = COLLECTION 'chome','form_input'
Forum = COLLECTION 'chome','forum'
GroupForumCol = COLLECTION 'chome','group_forum'

Wecard = COLLECTION 'chome', 'wecard'
Projects = COLLECTION 'chome', 'pre_constrct'
GroupModel = MODEL 'Group'
StatModel = MODEL 'Stat'

UserModel = MODEL 'User'
FormInputModel = MODEL 'FormInput'
PropertiesModel = MODEL 'Properties'
SysDataModel = MODEL 'SysData'
ProjectModel = MODEL 'Project'
# accessAllowed = DEF '_access_allowed'
getDotHtml = DEF 'getDotHtml'
NO_TARGET_USER_FOUND = 'No target user found, please contat admin for detail'
ERROR_WHEN_RENDER_EMAIL = 'Error when render email template'
OPEN_REALMASTER_TO_SEE_DETAIL = 'Open RealMaster App to see detail'
SIGN_UP_FORM_SUBMITTED = 'Your Signup form has been submitted'
SUBMISSION_FAILED = 'Submission Failed! Please try again later or contact us'
CONTACT_ON_NEXT_BUSINESS_DAY = 'We will contact you on next business day.'
CONTACT_ASAP = 'We will contact you ASAP.'
SUCCESSFULLY_SUBMITTED = 'Your request has been successfully submitted.'

FORM_INPUT_URL = '/1.5/form/forminput'
MAX_CHAT_LIMIT = 1000
ObjectId = INCLUDE('lib.mongo4').ObjectId
sendMail = SERVICE 'sendMail'

sysGroupNameIdMapping = DEF 'sysGroupNameIdMapping'

sysGroup = sysGroupNameIdMapping()
ProvAndCity = MODEL 'ProvAndCity'
SysNotifyModel = MODEL 'SysNotify'
popularCities = DEF 'popularCitiesWithOwnControlData'
CRM_FAILBACKEMAIL = '<EMAIL>'
FUB_SPECIFIC_EMAIL = config.followUpBoss?.specificEmail or '<EMAIL>'
DEFAULT_FROM_NAME = config.contact?.defaultEmailFromName or 'RealMaster'


getNewFormInput = (req, cb)->
  try
    user = await UserModel.getFromSession {req}
  catch err
    return cb 0
  unless user
    return cb 0
  try
    gids = await GroupModel.getUserGroupIDs user
    cnt = await FormInputModel.countNewFormInput user._id,gids
  catch err
    return cb false if err
  cb cnt

DEF 'getNewFormInput', getNewFormInput

APP '1.5'
APP 'form',true

{respError} = INCLUDE 'libapp.responseHelper'

GET (req,resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    return resp.ckup 'form-list',{},'_',{noAngular:true}

GET 'feedback', (req,resp) ->
  return resp.ckup 'feedback',{},'_',{noAngular:true}

VIEW "feedback",->
  div id: "vuebody", style:'height: 100%;padding-top: 50px;background: #f1f1f1;',->
    text """<feedback-form></feedback-form>"""
  js '/js/entry/commons.js'
  js '/js/entry/appFeedback.js'
# get my form list
GET 'list', (req,resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    uid = user._id
    query = {$or:[{uid:uid},{grp: 'global'}]}
    query = {} if req.hasRole '_admin'

    Form.findToArray query,{projection:{_id:1, nm:1, uid:1, fn:1, avt:1}},(err, ret)->
      #find used global form， find owner used post and regitsered user numer.
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp, sysErr:err} if err
      ids = []
      for form in ret
        ids.push form._id.toString()
      return resp.send {ok:1, formList: ret}

# get form detail
GET 'detail/:id', (req,resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless id = req.param 'id'
  Form.findOne {_id: id},(err, form) ->
    return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
    return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND),resp} unless form
    return resp.send {ok:1, form: form}

# under the form list, get my content of the form. admin can see all.
GET 'msgs',(req, resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    unless formid = req.param 'formid'
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
    FormInputModel.getMsgByFormId {uid:user._id,formid}, (err, ret) ->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND),resp} unless ret
      Form.findOne {_id:formid}, (err, form) ->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
        return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND),resp} unless form
        return resp.send {ok:1, contents: ret, fm: form.fm}

#new or update form
POST (req,resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p = req.body
  UserModel.appAuth {req,resp,userfields:['fn','avt']},(user)->
    unless user and UserModel.accessAllowed 'formOrganizer',user
      return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    if id = p._id
      unless ((p.uid is user._id.toString()) or req.hasRole '_admin')
        return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED), resp}
    form = {uid: user._id, fm:[], mt: new Date()}
    for nm in ['nm','grp','emlNotify','smsNotify']
      form[nm] = p[nm] if p[nm]

    for fld in p.fm
      field = {}
      for nm in ['key','nm','tp','fix','val','req','default','allowUrl','hidden']
        field[nm] = fld[nm] if fld[nm]?
      form.fm.push field

    if (not id)
      if /system/.test form.nm
        form._id = id = form.nm
      else
        form._id = id = new Form.ObjectId()
    form.fn = user.fn
    form.avt = user.avt
    update = {$set:form}
    Form.updateOne {_id: id}, update, {upsert: true}, (err, ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      resp.send {ok:1, _id: form._id, uid: form.uid, avt: form.avt, fn: form.fn}

# only delete empty form
POST 'delete',(req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p = req.body
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    FormInputModel.getIdsByFormId p.id,(err, contents) ->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      return resp.send {ok:0, err: req.l10n('Can not delete used form ')} if contents?.length
      Form.deleteOne {_id: p.id}, (err, ret)->
        return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
        resp.send {ok:1}

POST 'content/delete',(req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p=req.body
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless id = p.id
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    FormInputModel.deleteContent id, (err, ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      resp.send {ok:1}

# admin and forum owner can add memo
POST 'content/manageMemo',(req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p = req.body
  id = p.id
  # forumid = p.forumid
  # msgts = p.msgts
  memo = p.memo
  if (p.type not in ['add','del']) or (not id)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp,userfields:['nm_zh','nm_en','nm']},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    ret = {ok:1}
    try
      if p.type is 'add'
        ret.memo = await FormInputModel.addmemo {user, memo, id,locale:req.locale()}
      if p.type is 'del'
        await FormInputModel.delmemo {id, memots:p.ts}
    catch err
      return respError {clientMsg:req.l10n(err),resp}
    resp.send ret

# admin and forum owner can add memo
POST 'content/notify',(req, resp) ->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p= req.body
  # formid = p.formid
  # forumid = p.forumid
  msgids = p.msgids
  tp = p.tp
  m = p.m
  unless ['eml','sms','pn'].indexOf(p.tp)>=0
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  if (not msgids?.length) or (not m)
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp}
  UserModel.appAuth {req,resp,userfields:['eml','nm_zh','nm_en','nm']},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    FormInputModel.updateNotifies {ids:msgids,locale:req.locale(),user,tp,m}, (err, notify)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      # unless (content = ret?.value) and ret.lastErrorObject.n
      #   return respError MSG_STRINGS.NOT_FOUND,resp, err if err
      # notify.msgids = msgids
      FormInputModel.getListByIds {ids:msgids,projection:FormInputModel.NOTIFY_FIELDS},(err, ret)->
        opt =
          replyTo: if Array.isArray user.eml then user.eml[0] else user.eml
          subject: req.l10n('Notification for Form Input','form')
          engine: config.mailEngine?.mailEngine or 'SES' #'sendmail' #'gmail'

        opt.pn = 1 if tp is 'pn'
        opt.mail = 1 if tp is 'eml'
        opt.sms = 1 if tp is 'sms'
        u = {}
        sendMailList=[]
        for msg in ret
          #do not send to one user twice
          if sendMailList.indexOf(msg.eml) < 0
            u.mbl = msg.mbl or msg.tel
            u.eml = msg.eml
            u.pn = msg.fromPn
            sendMailList.push u

        for user in sendMailList
          SysNotifyModel.sendToUser user,opt,m,(err) ->
            debug.error err
        resp.send {ok:1, notify: notify}

POST 'read',(req, resp)->
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless id= req.body?.id
  read = req.body.read
  UserModel.appAuth {req,resp},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    FormInputModel.updateRead {id,read}, (err, ret)->
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
      resp.send {ok:1}

getUserEml = libUser.getUserEml
# getUserEml = (user)->
#   return if Array.isArray user.eml then user.eml[0] else user.eml

# give uid find user
find_user_by_uid = (uid)->
  uids = if Array.isArray(uid) then uid else [uid]
  if uids?.length
    users = await UserModel.getListByIds uids, {projection:{_id:1,eml:1,pn:1,mbl:1,fubLeadEml:1,fubAgentId:1,serviceCity:1}}
    return users if (users and users.length>0)
  return await find_default_email()

find_default_email = ()->
  user = await UserModel.findByEmlAsync config.contact?.defaultRTEmail, {propjection:{eml:1,pn:1,mbl:1}}
  unless user
    debug.error "find_default_email:#{MSG_STRINGS.USER_NOT_FOUND}"
  return user

# give uid find user
find_user_by_gid = (gid)->
  gids = if Array.isArray(gid) then gid else [gid]
  return null unless gids?.length
  grps = await GroupModel.findGroupsByIds gids
  return null unless grps?.length
  uids = []
  for grp in grps
    for m in grp.members
      uids.push m.uid
  return await find_user_by_uid uids

findUserByGid = (gid)->
  gid ?= sysGroup[':RmSales']
  () ->
    users = await find_user_by_gid gid
    return {tousers:users, gid}

###
# no page, used for form input from website.
# @input
# type param {
#   isWeb: type Boolean, # 来源web
#   src: type String, # 来源 'app'/'web'
#   tp: type String, # 表单类型
#   formid: type String, #固定值 'system'，用来获取表单模版参数，用于校验
#   url: type String,
#   uid: type String, # Target User
#   noInform: type Boolean,
#   nm: type String, # name
#   eml: type String,
#   mbl: type String,
#   pjid: type String, # project _id
#   id: type String, # project _id
#   img: type String,
#   projnm: type String, # project name
#   projnm_en: type String, # project name
#   ip: type String,
#   locale: type String, # language
#   hasWechat:
# }
###
findProjectToUser = ({input})->
  id = input.id
  src = input.src
  if src isnt 'app'
    src = 'web'
  uid = input.uid
  pjfloorid = input.pjfloorid
  if not id # from web project list page,no id
    gid = sysGroup[':RmProject']
  else if uid and pjfloorid # floor plan send to uid
    uid = new ObjectId(uid) if typeof uid is 'string'
  else if (src isnt 'app') and uid # for share send to uid who's sharing
    uid = new ObjectId(uid) if typeof uid is 'string'
  else
    #group is not supported for now.maybe add back in future
    uid = await ProjectModel.findSponsorUid id, src
    if not uid
      # debug.debug 'no sponor found for this project, send to RmProject group'
      gid = sysGroup[':RmProject']
  if gid and (not uid?.length)
    users = await find_user_by_gid gid
    return {tousers:users, gid}
  else
    users= await find_user_by_uid uid
    return {tousers:users, uid} unless gid?.length
    users = await find_user_by_gid gid
    return {tousers:users, uid, gid}

# old logic used in wecard, forum
findOwner = (col)->
  ({req}) ->
    isApp = req.getDevType() is 'app'
    id = req.param 'id'
    fields = {uid:1, meta:1, tl:1}
    ret = await col.findOne {_id: id}, {fields}
    return ret unless ret
    if req.param 'uid'
      uid = req.param 'uid'
      uid = new ObjectId(uid) if typeof uid is 'string'
    else if ret.uid
      uid = ret.uid
    return uid unless uid
    users = await find_user_by_uid uid
    return {tousers:users, uid, prop:ret}

#if no client side uid, sent to :RmProp group
findPropToUser = ({input})->
  if (uid = input.uid)
    uid = new ObjectId(uid) if typeof uid is 'string'
    users = await find_user_by_uid uid
    return {tousers:users, uid}
  else
    gid = sysGroup[':RmProp']
    users = await find_user_by_gid gid
    return {tousers:users, gid}

###
To imporove
used for choose agent out of GTA
###
chooseOneAgent = (users)->
  return users[helpers.randomTo(users.length - 1)]

###
find to user when user do not has flwngRm agent
@param {object} user, login user
@param {object} input, forminput, used to calc crmDest

send to lead source src.page.region
###
# used when user submit form for the first time
# TODO: might use later for checking agent out side gta but not in crm.
# currently only send to crm.
# findAgentWhenNoFollowedRm = (user,input)->
#   isGTA = propertiesHelper.isRMControlProp(input)
#   debug.debug 'isGTA',isGTA
#   if isGTA
#     toUser = { skipSendFormEmail:true}
#     toUser.crmDest = followUpBossLib.getFubSrc input, propertiesHelper.GTA
#     return toUser
#   else
#     # outsite Toroto,
#     # find by serviceArea and vip, random 找到一个经纪, send form to agent.
#     # send to crm group
#     projection = {eml:1}
#     agentInThisCity = await UserModel.findByServiceAreaAsync \
#       input.prov, input.city, {projection}
#     crmDest = followUpBossLib.getFubSrc input, propertiesHelper.OTHER_CITY
#     if not agentInThisCity.length # TODO: default send to crm salesgroup，是否应该发到黄页？
#       debug.warn "no agent found in this city, \
#         send to crm #{crmDest}",'input:', input
#       toUser = {crmDest, skipSendFormEmail:true}
#       return toUser
#     debug.debug 'agentInThisCity',agentInThisCity
#     toUser = chooseOneAgent(agentInThisCity)
#     if user # add flwngRm to this user
#       await UserModel.setFlwngRmByAgentIdAsync \
#         {userArray: [user],rmAgentId: toUser._id}
#     debug.debug 'chooseOneAgent',toUser
#     toUser.crmDest = crmDest
#     toUser.tag = input.eml
#     return toUser



FN_FIND_TO_USER =
  'wecard': findOwner Wecard
  'forum': findOwner Forum
  'groupforum': findOwner GroupForumCol
  'project': findProjectToUser
  'prop': findPropToUser  #for web prop detail
  'evaluationReport': findUserByGid sysGroup[':RmSales']
  'renovation': findUserByGid sysGroup[':RmSales']
  'predictReport': findUserByGid sysGroup[':RmSales']
  'toplisting': findUserByGid sysGroup[':RmTopListing']
  'toppreconstruction': findUserByGid sysGroup[':RmTopListing']
  'website': findUserByGid sysGroup[':RmSales']
  'feedback': findUserByGid sysGroup[':RmFeedback']
  'rmlisting': findUserByGid sysGroup[':RmSales']
  'other': findUserByGid sysGroup[':RmSales']
  # TODO: TRUSTED_ASSIGNMENT  should be here when have more email to send/more people to assign
  # 'trustedAssignment': findUserByGid sysGroup[':RmAssignment']

###
page:'landLordRent','project','mls','rmlisting','evaluation','buyTrustedAssign','trustedAssignment','saleTrustedAssign','assignment'
page exist: use setBusinessLogicByPage
page inexistence: using tp
###
getFind2UserFn = (tp,page)->
  if tp and not page
    return FN_FIND_TO_USER[tp]
  return findToUsersWhenNoTp

###
using page
根据 action.formDest 里的数据确定 target，获取收件人或者fub信息
return：{object} - {tousers, checkDuty，gid}
###
findToUsersWhenNoTp = ({input,user})->
  {src,hasWechat,page} = input
  ret = await contactRealtorHelper.getContactRealtorAction {
    hasWechat,
    user,
    page,
    src,
    prop:input,
    ProjectModel,
    PropertiesModel,
    UserModel,
    popularCities,
    SysDataModel
  }
  return null unless ret
  {action, owner, followedAgentInfo,key,specificEmail} = ret

  debug.formInput input.eml,'step2',key,helpers.stringify(action)

  # followedAgentInfo is followed rm agent info in here
  tousers = []
  if not action.formDest
    debug.error 'no action formdest for input:',input, 'user:',user
    return null
  #send to crmDestUser
  if CONTACT_REALTOR_VALUES.UPGRADE_VIP in action.formDest
    # for email subject. for forum Complaint will send this from front end.
    findUserByGidFn = findUserByGid sysGroup[':RmSales']
    result = await findUserByGidFn()
    result.subjectTp = TITLE_STR.UPGRADE_TO_VIP
    return result
  for dest in action.formDest
    if dest is CONTACT_REALTOR_VALUES.CRM
      user = {}
      if followedAgentInfo
        user = Object.assign {}, followedAgentInfo
      else if owner and ((not owner.roles) or owner.roles?.indexOf('_crmNoAssign') < 0)
        # 优先发给follow，然后发给owner
        user = Object.assign {}, owner
      else
        checkDuty = true
      user.crmDest = user.fubLeadEml or 'CRM'
      user.skipSendFormEmail= true
      tousers.push user
    #send email to owner
    if dest in [
      CONTACT_REALTOR_VALUES.OWNER,
      CONTACT_REALTOR_VALUES.LISTING_AGENT,
      CONTACT_REALTOR_VALUES.PROJECT_SPONSOR,\
      CONTACT_REALTOR_VALUES.RM_LISTING_OWNER,\
      CONTACT_REALTOR_VALUES.TOP_LISTING_AGENT
    ]
      tousers.push owner
    #send email to followed rm realtor.
    if dest is CONTACT_REALTOR_VALUES.FOLLOWED_RM_REALTOR
      tousers.push followedAgentInfo
    # project，部分楼花转让发送到指定邮箱，receiveUser 保存的是指定邮箱的用户信息
    if dest in [CONTACT_REALTOR_VALUES.FUB_ASSIGNMENT_EMAIL,\
    CONTACT_REALTOR_VALUES.FUB_PROJECT_EMAIL]
      tousers.push specificEmail
  return {tousers, checkDuty}

# system form allowed fields
# wecard, forum, need meta data.
# formid:'system', tp:'wecard',id:wpid, nm:nm, eml:eml, wxid:wxid, mbl:mbl, m:m, img:meta.img, url:url, wpid:wpid
# forum forumid
# project floorid,
# prop: get from req: price, addr, city, prov, img,
# renovation
      # self.userForm.addr = self.curProj.addr;
      # self.userForm.city = self.curProj.city_en;
      # self.userForm.prov = self.curProj.prov_en;
      # self.userForm.projnm = self.curProj.nm;
      # self.userForm.projnm_en = self.curProj.nm_en;
      # self.userForm.img = self.curProj.src;
      # self.userForm.url = self.shareUrl;


# formatType = (key)->
#   tpMap =
#     'prop': 'Property'
#     'project': 'Project'
#     'forum': 'Forum'
#     'wecard' : 'Wecard'
#   if tpMap[key]
#     return tpMap[key]
#   else
#     return key


blockInputForThisIp = (req)->
  return (not req.isDeveloperMode()) and req.countAndReturnFormInputBlocked? \
    and req.countAndReturnFormInputBlocked()

###
# /1.5/form/forminput
#:
@param {string} body.tp: optional, default is other. to determin which user to send.
@param {string} body.uid: project or prop owner
@param {string} body.nm : sender's name
@param {string} body.eml  sender's email
@param {string} body.mbl  sender's mbl
@param {string} body.m
@param {string} body.sid  MLS id
@param {string} body.id   rm id
@param {string} body.lp
@param {string} body.url  source url
@param {string} body.city
@param {string} body.prov
@param {string} body.lpr
@param {string} body.addr
# @param {string} body.saletp
# @param {string} body.ptype
# @param {string} body.bdrms
# @param {string} body.bthrms
# @param {string} body.zip
# @param {string} body.sqft
@param {string} body.page: 'landLordRent','project','mls','rmlisting','evaluation','buyTrustedAssign','trustedAssignment','saleTrustedAssign'
expected:
save forminput to db
find receivers.
send mail to receiver, crm, and also a copy to sender
###
POST 'forminput',(req, resp) ->
  # check if form input is blocked for this ip or this session
  dealSysError = ({sysErr,clientMsg,adminMsg})->
    debug.error 'forminput sysErr', sysErr, 'User Input: ', req.body
    SysNotifyModel.notifyAdmin "#{adminMsg or 'forminput error: '}\
      #{helpers.stringify(sysErr)} #{helpers.stringify(req.body)}"
    return respError {clientMsg:clientMsg or SUBMISSION_FAILED, resp, sysErr}

  dealBadParamError = (sysErr,clientMsg)->
    debug.error 'forminput bad param sysErr', sysErr, 'User Input: ', req.body
    return respError {clientMsg:clientMsg or SUBMISSION_FAILED, resp, sysErr}
    
  dealRespSuccess = (checkDuty)->
    if checkDuty
      SysDataModel.findById 'contactRealtor',(err, contactRealtorData)->
        if contactRealtorData?.isWorking
          return resp.send {ok:1, msg: req.l10n CONTACT_ASAP}
        else
          return resp.send {ok:1, msg: req.l10n CONTACT_ON_NEXT_BUSINESS_DAY}
    else
      return resp.send {ok:1, msg: req.l10n SUCCESSFULLY_SUBMITTED}


  unless input = req.body
    return dealBadParamError \
      MSG_STRINGS.BAD_PARAMETER, MSG_STRINGS.BAD_PARAMETER

  debug.formInput input.eml,'step1',helpers.stringify(input)

  input.ip = req.remoteIP()
  input.locale ?= req.locale()
  input.src ?= req.getDevType()
  if input.fn and input.ln
    if hasChinese(input.fn) and hasChinese(input.ln)
      input.nm = "#{input.ln}#{input.fn}"
    else
      input.nm = "#{input.fn} #{input.ln}"
  {page,locale,tp,src,hasWechat} = input

  if page #handle contact realtor form
    input.url = getPropShareUrl \
      {prop:input,page,locale,shareHost:config.serverBase?.wwwDomain}

  UserModel.appAuth {req,resp,userfields:['splang','locale','eml','nm_zh','nm_en','nm']},(user)->
    user ?= {}
    splang = user.splang?.join(',')
    input.locale = splang or user.locale or req.locale()
    input.hasWechat  =  UserModel.hasWechat(user,{hasWechat})

    if user._id
      try
        user = await UserModel.findByIdAsync user._id, \
          {projection:UserModel.FORM_INPUT_FIELDS}
      catch error
        return dealSysError({sysErr:error})
    #parse form input
    try
      {formatedFormInput,form, validationError} = await \
        FormInputModel.parseFormInputAsync {user,input}
      if validationError
        return dealSysError {sysErr:validationError, clientMsg:req.l10n(validationError)}
    catch error
      return dealSysError {sysErr:error}
    if user.roles
      formatedFormInput.notes = "roles:#{user.roles}"
    # add geoInfo to formatedFormInput
    if geoInfo = req.getGeoInfo(true)
      formatedFormInput.geoInfo = geoInfo
      if formatedFormInput.notes
        formatedFormInput.notes += " geoInfo:#{geoInfo}"
      else
        formatedFormInput.notes = "geoInfo:#{geoInfo}"
    # find signup target user
    findTargetUser = getFind2UserFn tp,page
    if not findTargetUser
      return dealSysError {sysErr:'no target function found'}
    try
      ret = await findTargetUser {req, user, input}
    catch err
      return dealSysError {sysErr:err}
    
    if input.tp and not input.page
      debug.formInput input.eml,'step2',helpers.stringify(ret)
    else
      debug.formInput input.eml,'step3',helpers.stringify(ret)
    
    if not ret
      return dealSysError {sysErr:'No result for findTargetUser'}

    {tousers, uids, gids, prop, checkDuty, subjectTp} = ret
    tousers = if Array.isArray(tousers) then tousers else [tousers]
    if subjectTp
      formatedFormInput.subjectTp = subjectTp
    unless tousers?.length
      return dealSysError {sysErr:"#{NO_TARGET_USER_FOUND}"}
    injectUserIds({uids, gids, tousers, formatedFormInput})
    injectTitle(formatedFormInput, prop)
    isTruAssignPage = (page in [CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT,\
    CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT,\
    CONTACT_REALTOR_VALUES.PROJECT])
    try
      usersToCrm = tousers.filter (user)-> return user.crmDest
      if (usersToCrm.length is 1) and usersToCrm[0].crmDest and (not isTruAssignPage)
        # because only one user needed in crm, 通过api发送
        # skip send lead to crm in unit test
        await sendLeadToCrm {req, formatedFormInput,tousers:usersToCrm,user}
        fubApi = true
      await FormInputModel.insertOneAsync formatedFormInput
      debug.formInput input.eml,'step3or4 insert form_input'
    catch error
      debug.info formatedFormInput,usersToCrm
      return dealSysError {sysErr:error,adminMsg:'sendLeadToCrm error: '}
    if input.tp is 'project' # 需求是只有project的房源才需要做统计
      try
        await StatModel.updateStat {tp:'forminput',id:formatedFormInput.id,stat:input.rmsrc}
      catch err
        # this error is not critical, so we just log it.
        debug.error err
        debug.error "StatModel.updateStat error: id:#{formatedFormInput.id}, stat:#{input.rmsrc}"
    # 发给FUB API/email的信息，发送到指定邮箱
    if fubApi
      sendSpecificEmailForFub {req, formatedFormInput,target:'Fub Api'}

    if blockInputForThisIp(req)
      return dealBadParamError MSG_STRINGS.ACCESS_DENIED
    # 真楼花/PerCon/部分assignment发送到指定邮箱
    if (isTruAssignPage or ((page is CONTACT_REALTOR_VALUES.ASSIGNMENT) and (not fubApi)))
      sendFubSpecifiedEmail {
        req,
        formatedFormInput,
        tousers:tousers[0].eml,
        user,
        page,
        src
      },(err,response) ->
        if err
          return dealSysError {sysErr:err}
        debug.formInput input.eml,"finally:#{formatedFormInput.fubTags or 'trustedAssignment'}"
        # 发给FUB API/email的信息，发送到指定邮箱
        sendSpecificEmailForFub {req, formatedFormInput,target:'Specific Email'}
        return dealRespSuccess(checkDuty)
    else
      emlTemplateData = formInputLib.getEmailTemplateData formatedFormInput,form
      #exists case both send to crm and owner/sponsor
      sendEmailToReceivers \
      {req, formatedFormInput, emlTemplateData, tousers, user},(err,ret)->
        if err
          return dealSysError {sysErr:err}
        debug.formInput input.eml,'finally sendEmailToReceivers'
        #form.emlNotify is notify sender. default is false
        if not formatedFormInput.notifySender or not formatedFormInput.eml
          return dealRespSuccess(checkDuty)
        #currently default form system is set to not send to sender.
        sendEmailToSender {req, emlTemplateData,formatedFormInput},(err,ret)->
          if err
            return dealSysError {sysErr:err}
          return dealRespSuccess(checkDuty)

injectUserIds = ({uids, gids, tousers, formatedFormInput}) ->
  if uids
    uids = if Array.isArray(uids) then uids else [uids]
    formatedFormInput.uids = uids
  if gids
    gids = if Array.isArray(gids) then gids else [gids]
    formatedFormInput.gids = gids
  formatedFormInput.tousers = []
  for u in tousers # save this in form_input col
    touser = {}
    touser.eml = getUserEml u if u.eml
    touser.uid = u._id if u._id
    for fld in ['fubLeadEml','fubAgentId','skipSendFormEmail','crmDest']
      touser[fld] = u[fld] if u[fld]
    formatedFormInput.tousers.push touser

injectTitle = (formatedFormInput,prop)->
  #inject Wecard， forum content
  if formatedFormInput.tp  is 'wecard'
    formatedFormInput.tl = prop?.meta?.title
  if formatedFormInput.tp in ['forum','groupforum']
    formatedFormInput.tl = prop?.tl


#TODO: check if req can be removed from getDotHtml
#crm 用户 skipSendFormEmail = true
# 外地经纪，既要发crm，也要发email

sendEmailToReceivers = ({req, formatedFormInput,emlTemplateData,tousers,user},cb)->
  #remove users which do not need to send
  usersToSendEmail = tousers.filter (user)-> return not user.skipSendFormEmail
  debug.debug 'sendEmailToReceivers usersToSendEmail',usersToSendEmail
  if not usersToSendEmail.length
    return cb()
  sendFormEmail {req, formatedFormInput,emlTemplateData,tousers:usersToSendEmail,user},cb


sendFormEmail = ({req, formatedFormInput,emlTemplateData,tousers,user},cb)->
  cb = helpers.callOnce cb
  mailBody = getDotHtml req, 'signupForm', emlTemplateData
  if mailBody.err
    debug.error mailBody.err
    return cb(ERROR_WHEN_RENDER_EMAIL)

  #subjectTp:"Forum Complaint"
  debug.debug 'emlTemplateData',emlTemplateData, 'formatedFormInput.subjectTp',formatedFormInput.subjectTp
  subject = formatedFormInput.subjectTp \
    or formInputLib.formatType(formatedFormInput.tp or formatedFormInput.page)
    
  if formatedFormInput.eml or formatedFormInput.mbl
    subject = "[#{subject}] #{req.l10n('Request Info from user:','form')} \
    #{formatedFormInput.eml or formatedFormInput.mbl} "

  mailConfig =
    from: req.l10n(DEFAULT_FROM_NAME)
    subject: subject
    engine: config.mailEngine?.mailEngine or 'SES'
    replyTo: formatedFormInput.eml
    mail: 1
    url: FORM_INPUT_URL
    shortBody:"#{req.l10n(OPEN_REALMASTER_TO_SEE_DETAIL,'form')} "
  debug.debug 'mailConfig',mailConfig
  #send sms and pn to project
  # if formatedFormInput.tp is CONTACT_REALTOR_VALUES.PROJECT
  #   mailConfig.pn = 1
  #   mailConfig.sms = 1

  
  for emailUser in tousers
    eml = getUserEml emailUser
    # emailUser.eml = '<EMAIL>' #for local test
    debug.debug  "send mail to recieiver #{emailUser.eml}"
    # 等待email 发送成功在返回。
    SysNotifyModel.sendToUser emailUser, mailConfig, mailBody.html, (err,response)->
      if err
        debug.error err
        return cb(err) #only cb once.
      return cb()

sendEmailToSender = ({req, emlTemplateData, formatedFormInput, form},cb)->
  emlTemplateData.sender = true
  mailBody = getDotHtml req, 'signupForm', emlTemplateData
  if mailBody.err
    debug.error mailBody.err
    return cb(ERROR_WHEN_RENDER_EMAIL)
  fromUser = { eml : formatedFormInput.eml} #use the eml that specified.
  mailConfig =
    from: req.l10n(DEFAULT_FROM_NAME)
    subject: req.l10n(SIGN_UP_FORM_SUBMITTED,'form')
    engine: config.mailEngine?.mailEngine or 'SES'
    replyTo: config.contact?.defaultRTEmail
    mail: 1
    url: FORM_INPUT_URL
    shortBody:"#{req.l10n(OPEN_REALMASTER_TO_SEE_DETAIL,'form')} "
  SysNotifyModel.sendToUser fromUser, mailConfig, mailBody.html, (err,response)->
    debug.error "send mail to from user #{formatedFormInput.eml}"
    debug.error err if err
    return cb(err)

sendLeadToCrm = ({req, formatedFormInput,tousers,user})->
  # if should not send to crm
  debug.debug 'sendLeadToCrm',tousers,formatedFormInput,user
  if config.serverBase?.skipSendLeadToCrm # for unit test purpose
    debug.info 'skipSendLeadToCrm',formatedFormInput
    return 
  formatedFormInput = followUpBossLib.appendFubInfo(formatedFormInput)
  fubToken = config.followUpBoss?.token
  if not fubToken
    err = 'NO follow up boss token in config'
    debug.error err
    throw err

  param = {
    formInputData:formatedFormInput,
    # assignedUserId: tousers[0].fubAgentId, 20220922 conf fred/rain, no need
    fubToken
  }
  crmErr = null
  #send to crm
  try
    crmResult = await followUpBossLib.sendToCrmWithApiAsync param
  catch err
    debug.error 'sendLeadToCrm err',err
    crmErr = err.toString()
    # sent <NAME_EMAIL> if error happened
    tousers = config.contact?.failBackEmail or CRM_FAILBACKEMAIL
    sendFubSpecifiedEmail {req, formatedFormInput,user,tousers},(err) ->
      if err
        debug.error 'send email failed when after post followupboss api failed, error:',err
    throw err
  finally
    # 官方文档Responses给出200和404返回Object，201和204返回json
    # 先用JSON.stringify看是否会报错，确认第三方返回的数据是标准的Object或Json
    if crmResult
      try
        crmResultStringify = JSON.stringify(crmResult)
      catch err
        debug.error 'sendLeadToCrm finally stringify crmResult', err, crmResult
        crmResultStringify = helpers.stringify(crmResult)
      crmResult = JSON.parse crmResultStringify
    fubCrmResult = Object.assign {param},(crmResult or {})
    if crmErr
      fubCrmResult.err = crmErr
    debug.debug 'fubCrmResult',fubCrmResult
    debug.formInput formatedFormInput.eml,'finally:sendLeadToCrm,saveFubLeadResult',helpers.stringify(fubCrmResult)
    # save result, set follow
    # 将该部分catch起来主要是因为如果我们自己保存数据库失败，但是用户应该看到的是发送成功的界面。
    try
      await FormInputModel.saveFubLeadResultAsync fubCrmResult
      if not crmErr and user and crmResult and crmResult.id
        await UserModel.setUserFubInfo {
          eml: user.eml
          fubId: crmResult.id
          assignedUserId: crmResult.assignedUserId
        }
    catch err
      debug.error 'sendLeadToCrm finally err',err

# 真楼花/Percon/部分Assignment邮件内容发送fub格式
# https://help.followupboss.com/hc/en-us/articles/360015370573-Follow-Up-Boss-Email-Parser
# @input
# type param {
#  req: type object, # request object
#  formatedFormInput,
#  tousers: type string, # email of receive email user,
#  user: type object, # current signingup user,
#  page: type string
#  src: type string # app or web
# }
sendFubSpecifiedEmail = ({req, formatedFormInput,tousers,user,page,src},cb) ->
  # 邮件内容加上当前用户的角色
  if page is CONTACT_REALTOR_VALUES.PROJECT
    formatedFormInput.fubSrc = "#{src or 'unknown'}.PreCon"
    formatedFormInput.fubTags = 'PreCon'
    formatedFormInput.desc = 'PreCon'
  else if page is CONTACT_REALTOR_VALUES.ASSIGNMENT
    formatedFormInput.fubSrc = "#{src or 'unknown'}.Assignment"
    formatedFormInput.fubTags = 'Assignment'
    formatedFormInput.desc = 'Assignment'
  else
    formatedFormInput.fubSrc = "#{src or 'unknown'}.trustedAssignment"
  if (page is CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT)
    formatedFormInput.fubTags = 'BuyAssign'
    formatedFormInput.m = "To Buy Assignment: #{formatedFormInput.m}"
    formatedFormInput.desc = 'To Buy Assignment'
  if (page is CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT)
    formatedFormInput.fubTags = 'SellAssign'
    formatedFormInput.m = "Assign My Condos Or APS: #{formatedFormInput.m}"
    formatedFormInput.desc = 'Assign My Condos Or APS'
  mailBody = getDotHtml req, 'leadEmailToFollowUpBoss', formatedFormInput
  # console.log '+++++',mailBody
  if mailBody.err
    debug.error mailBody.err
    return cb(ERROR_WHEN_RENDER_EMAIL)
  # return cb()
  engine = config.mailEngine?.mailEngine or 'SES' #or 'sendmail'
  mailConfig =
    engine: engine # appConfig.mailEngine or 'SES'
    from: sendMail.getFromEmailByEngine(engine) # 发件人
    # replyTo: appConfig.defaultRTEmail # 回复邮箱
    to: tousers
    subject: 'Trusted Assignment form'
    # text: textbody
    html: mailBody.html
    isNeedValidate: true
    eventType: 'TrustedAssignmentEmail'
  sendMail.sendMail mailConfig,(err,response)->
    if err
      debug.error err
      return cb(err)
    return cb()

# 发给FUB API/email的信息，发送到指定邮箱, 添加target说明来源
sendSpecificEmailForFub = ({req,formatedFormInput,target})->
  tmpFormInput = Object.assign {},formatedFormInput
  tmpFormInput.remoteIP = req.remoteIP()
  tmpFormInput.target = target
  mailBody = getDotHtml req, 'leadEmailToFollowUpBoss', tmpFormInput
  if mailBody.err
    debug.error mailBody.err
    return
  engine = 'rmMail'
  mailConfig =
    engine: engine
    from: sendMail.getFromEmailByEngine(engine) # 发件人
    to: FUB_SPECIFIC_EMAIL # 发送到指定邮箱
    subject: 'FUB Target'
    html: mailBody.html
    listId: 'SendSpecificEmailForFub'
    eventType: 'SendSpecificEmailForFub'
  sendMail.sendMail mailConfig,(err,response)->
    if err
      debug.error err
    return

#dont need this for now
# sendEmailToFollowUpBoss = ({req, formatedFormInput, leadEml},cb)->
#   #required fields in opt：user.nm,user.eml
#   #fubSrc(定义， Source，用type还是app， website等，目前只有app)
#   debug.debug 'sendEmailToFollowUpBoss'
#   mailBody = getDotHtml req, 'leadEmailToFollowUpBoss', formatedFormInput
#   if mailBody.err
#     return cb('Error when render email template')
#   user = { eml : leadEml}
#   mailConfig =
#     from: 'RealMaster'
#     subject: req.l10n(CONTACT_REALTOR_VALUES.PROPERTY_INQUIRY)
#     engine: appConfig.mailEngine or 'SES'
#     replyTo: appConfig.defaultRTEmail
#     mail:1
#   debug.debug 'mailBody.html', mailBody.html, mailConfig
#   SysNotifyModel.sendToUser user, mailConfig, mailBody.html, (err,response)->
#     debug.debug  "send mail to follow up boss #{leadEml}"
#     debug.error err if err
#     return cb(err)
        # Forminput.insertOne opt, (err, ret)->
        #   return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err} if err
        #   data =
        #     keys: Object.keys(emailOpt)
        #     opt: emailOpt
        #     eml: opt.eml
        #     url: opt.url
        #     img: opt.img
        #     fromEml: opt.fromEml
        #     mbl:opt.mbl
        #     id: opt.id
        #     sid: opt.sid
        #     ip: req.remoteIP()
        #   if opt.addr
        #     data.addr= "#{opt.addr},#{opt.city},#{opt.prov}"
        #   ret = getDotHtml req, 'signupForm', data
        #   if ret.err
        #     return respError {clientMsg:req.l10n('Error when render email template'),resp, sysErr:err}
        #   html_owner = ret.html

        #   tp = opt.tp
        #   if TP2Word[tp]
        #     tp = TP2Word[tp]
        #   tp = subjectTp if subjectTp
        #   title = opt.title or req.l10n('Request Info from user:','form')
        #   if opt.eml or opt.mbl
        #     subject = "[#{tp}] #{title}: #{opt.eml or opt.mbl} "
        #   else
        #     # no user login report for forum
        #     subject = tp

        #   mailConfig =
        #     from: req.l10n('RealMaster', 'form')
        #     subject: subject
        #     engine: appConfig.mailEngine or 'SES'
        #     replyTo: opt.eml
        #     mail:1
        #     url:'/1.5/form/forminput'
        #     shortBody:"#{req.l10n('Open RealMaster App to see detail','form')} "
        #   if form.pn
        #     mailConfig.pn = form.pn
        #   if form.sms
        #     mailConfig.sms = form.sms

        #   #send sms and pn to project
        #   if tp is 'project'
        #     mailConfig.pn = 1
        #     mailConfig.sms = 1

        #   for emailUser in tousers
        #     eml = getUserEml emailUser
        #     # emailUser.eml = '<EMAIL>'
        #     console.log  "send mail to to user #{emailUser.eml}"
        #     SysNotifyModel.sendToUser emailUser, mailConfig, html_owner, (err,response)->
        #       console.log err if err
        #   if form.emlNotify and opt.eml
        #     # ret = getHtml req, 'signupFormConfirm', data
        #     # if ret.err then return respError req.l10n 'Error when render email template',resp, err if err
        #     # html_confirm = ret.html
        #     data.sender = true
        #     ret = getDotHtml req, 'signupForm', data
        #     if ret.err
        #       return respError {clientMsg:req.l10n('Error when render email template'),resp, sysErr:err}
        #     html_sender = ret.html
        #     fromUser = { eml : opt.eml} #use the eml that specified.
        #     mailConfig.subject = req.l10n('Your Signup form has been submitted','form')
        #     mailConfig.replyTo = appConfig.defaultRTEmail
        #     SysNotifyModel.sendToUser fromUser, mailConfig, html_sender, (err,response)->
        #       console.log  "send mail to from user #{opt.eml}"
        #       console.log err if err
        #   resp.send {ok:1}

GET 'forminput',(req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    return resp.ckup 'form-input',{},'_',{noAngular:true}

POST 'myforminput', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    return respError {clientMsg:req.l10n(MSG_STRINGS.ACCESS_DENIED),resp} unless user
    return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless p = req.body
    try
      #find user gid
      gids = await GroupModel.getUserGroupIDs user
      {contents,forms} = await FormInputModel.getUserFormInput {uid:user._id,gids,p}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err}
    return resp.send {ok:1, contents, forms}

POST 'srcList',(req, resp)->
  tp = req.param 'tp'
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER),resp} unless tp
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN, resp} unless user
    try
      #find user gid
      gids = await GroupModel.getUserGroupIDs user
      list = await FormInputModel.aggregateSrc {user,tp,gids}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err}
    srclist = []
    for tmp in list
      srclist.push tmp._id
    return resp.send {ok:1, srclist:srclist}

POST 'filterList',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN, resp} unless user
    try
      #find user gid
      gids = await GroupModel.getUserGroupIDs user
      tplist = await FormInputModel.getFilterList {user,gids}
    catch err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),resp, sysErr:err}
    return resp.send {ok:1, tplist:tplist}

VIEW "form-list",->
  div id: "list", style:'height: 100%;overflow: hidden;',->
    text """<app-form-list></app-form-list>"""
  js '/js/entry/commons.js'
  js '/js/entry/formList.js'

VIEW "form-input",->
  div id: "list", style:'height: 100%;overflow: hidden;',->
    text """<app-form-input></app-form-input>"""
  js '/js/entry/commons.js'
  js '/js/entry/appFormInput.js'
