SEARCH_LIMIT_NONUSER = 77
SEARCH_LIMIT_USER = 100

Properties = MODEL 'Properties'
AddrCache = MODEL 'AddrCache'
libP = INCLUDE 'libapp.properties'
libPropSearch = INCLUDE 'libapp.propSearch'
libMapServer =  INCLUDE 'lib.mapServer'
{keepAlphanumericToCDB} = INCLUDE 'lib.helpers_string'
config = CONFIG(['serverBase','share'])
UserModel = MODEL 'User'
debug = DEBUG('autocomplete')
propTranslate = INCLUDE 'libapp.propertiesTranslate'
getUserCity = DEF 'getUserCity'
{setPropWebUrl,blurProp} = INCLUDE 'libapp.propWeb'
MAX_ADDR_LENGTH = 100
GroupModel = MODEL 'Group'
# rateLimiter = INCLUDE 'lib.rateLimiter'

gFnError = (resp,err)->
  resp.send {ok:0, e:err}

APP '1.5'
APP 'props', true
#autocomplete search
# requestStdFn('addrSearch',{s:val,limit:10,loc:1,ts:this.addrSearchTs},cb1)
addrSearch = ({
  user,
  limit,
  s,   # *req, search string
  loc,
  req, # *required
  ts   # front-end req device ts, uniq search id
},cb)->
  opt = arguments[0]
  param = libP.getBaseForPropList req,user
  try
    isRealGroup = await GroupModel.isInGroup {uid:user?._id,groupName:':inReal'}
  catch err
    debug.error 'GroupModel.isInGroup',err
  param = Object.assign {
    user: user,
    limit: limit,
    input: keepAlphanumericToCDB(s),
    # req: opt.req,
    incLimit: true,
    isRealGroup
  },param
  # libP.genSearchMergeOrDelProps param

  Properties.inputSearch param, (err,retObj)->
    return cb(err) if err
    # NOTE: cannot translate_rmprop_list here, obj list already mapped

    # configShowSoldPrice = getConfig('showSoldPrice')
    FilterPropSplstFiledOpt = {isAllowedPropAdmin:param.isPropAdmin,isAllowedVipUser:param.isVipUser}
    isAssignAdmin = req.isAllowed('assignAdmin')
    #NOTE: before search only know addr, so filter after got result
    
    isAllowedPropManage = req.isAllowed('propManage')
    Properties.inputRMListingsSearch {isAllowedPropManage,limit,uid:user?._id,input:keepAlphanumericToCDB(s),props:retObj.list,isAssignAdmin},(err,rmProps)->
      return cb(err) if err
      retObj.list = rmProps
      retObj.cnt = retObj.cnt or rmProps.length

      list2 = []
      req.setupL10n() unless req._ab
      try
        props = await Properties.addPropFavFields {props:retObj.list,uid:user?._id}
      catch err
        debug.error err,'Properties.addPropFavFields'
      for p in props
        showValid = libP.filterPropSpLstField(FilterPropSplstFiledOpt,p)
        # debug.debug  '+++++showValid',showValid
        if showValid
          libP.setUpPropLogin(false,user, p)
          Properties.addPropAdditionalFields({
            user,prop:p,
            isAllowedPropAdmin:param.isPropAdmin,
            isAllowedVipUser:param.isVipUser,
            # configShowSoldPrice,
            l10n:(a,b)->req.l10n(a,b),
            _ab:(a,b)->req._ab(a,b),
            apisrc:param.apisrc,
            isRealGroup,
            simplfyPtp:false})
          # libP.genListingPicReplaceUrls(p, req.isChinaIP(), \
          #   {shareHostNameCn:config.share?.hostNameCn,use3rdPic:config.serverBase?.use3rdPic,isPad:req.isPad(),isThumb:1})
          
          Properties.deletePropPriviteFields({prop:p,user,isListData:true})
          setPropWebUrl(req,p)
          isApp = req.getDevType() is 'app'
          blurProp({prop:p,isApp,MSG_STRINGS}) if p.login
          list2.push p
      list2 = propTranslate.translate_rmprop_list(req, list2)
      cnt = retObj.cnt
      return cb null,{ok:1, l:list2, cnt, ts}

# TODO: if isAddressInput, get suggest Address and display in front end use stdFunctions
# addrs = retObj.suggestAddresses
# console.log '++++',addrs


ADDSTDFN 'addrSearch',{needReq:true,userObject:true},addrSearch

isReachedLimit = ({req,user})->
  evt = 'autocomplete'
  # appAuth req,resp,null, (user) ->
  searchCnt = req.session.get 'searchCnt'
  unless searchCnt
    searchCnt = 0
  searchCnt += 1
  req.session.set 'searchCnt',searchCnt
  limit = SEARCH_LIMIT_NONUSER
  if user
    limit = SEARCH_LIMIT_USER
  ret = searchCnt > limit
  debug.error "#{evt} reached limit(#{searchCnt}). User:#{user?._id or req.ip}:#{user?.eml}" if ret
  ret

CANADA_BORDER_SW = [42.057198, -137.963183]
CANADA_BORDER_NE = [71.077688, -54.357390]

# TODO: use canada lat/lng polygon shape
isValidCanadaLocation = ({lat,lng})->
  validLat = lat > CANADA_BORDER_SW[0] and lat < CANADA_BORDER_NE[0]
  validLng = lng > CANADA_BORDER_SW[1] and lng < CANADA_BORDER_NE[1]
  return validLat and validLng
  
# NOTE: mapbox不支持ip直接传参获得ip相关结果，只能获取服务器所在地区结果，所以要我们自己server先从ip获取lat,lng
# NOTE: ip->lat,lng, restrict lat,lng range
  # 0, user session lat,lng
  # 1, use user loc history, (DB user_profile)
  # 2, use user ip, if outoubound use #3
  # 3, use user default city
  # 4, lat, lng -> user.session
SESSION_LAT_LNG = 'user.latlng'
getReqLatLng = (req,user)->
  if sessionValue = req.session.get SESSION_LAT_LNG
    # console.log '++++hassession value'
    return sessionValue
  if user
    ret = await UserModel.getLastLocation(user._id)#UserProfile.find({_id:user._id},{lastLoc:1})
    if ret?.lastLoc
      # {lat,lng,ip,ua}
      req.session.set SESSION_LAT_LNG,ret.lastLoc
      return ret.lastLoc
  if req.getIpCity? and (ip = req.remoteIP())
    cityObj = req.getIpCity()
    # console.log '++++++@@@@',cityObj
    if loc = cityObj?.location
      ret = {lat:loc.latitude,lng:loc.longitude,ip}
      if isValidCanadaLocation(ret)
        req.session.set SESSION_LAT_LNG,ret
        return ret
  defaultCity = getUserCity(req,user,null)
  # console.log '++++defaultCity value'
  if defaultCity?.lat?
    return defaultCity
  return {lat:0,lng:0}

addrSuggest = (opt,cb)->
  {req,user,s} = opt
  s ?= ''
  searchAddr = keepAlphanumericToCDB(s.trim().toLowerCase()).slice(0,MAX_ADDR_LENGTH)
  if isReachedLimit {req,user}
    return cb(MSG_STRINGS.EXCEED_QUOTA)
  try
    {lat,lng} = await getReqLatLng(req,user)
  catch err
    debug.error "getReqLatLng uid:#{user?._id},error:",err
    return cb(err)
    # console.log('++++++',lat,lng)
  try
    addressRet = await AddrCache.findSearchAddress {ip:req.remoteIP(),search:searchAddr,op:'search',lat,lng}
  catch err
    debug.error "findSearchAddress search operation error, search:#{searchAddr},error:",err
    return cb(err)
    # console.log '+++++',addressRet
  if addressRet?.suggest?.length
    addressRet.ok = 1
    return cb(null,addressRet)
  try
    suggestAddresses = await libMapServer.getAddressSearchByMapbox {addr:searchAddr,ip:req.remoteIP(),lat,lng}
  catch err
    debug.error "getAddressSearchByMapbox error,addr:#{searchAddr},error:",err.toString()
    return cb(err)
  try
    await AddrCache.findSearchAddress {search:searchAddr,addresses:suggestAddresses,op:'save',lat,lng}
  catch err
    debug.error "findSearchAddress save operation error, search:#{searchAddr},addr:#{suggestAddresses},error:",err
    return cb(err)
    # console.log '+++++2',suggestAddresses
  return cb(null,{ok:1,_id:searchAddr,suggest:suggestAddresses})

ADDSTDFN 'addrSuggest',{needReq:true,userObject:true},addrSuggest

# /1.5/props/addrSuggest
POST 'addrSuggest',(req,resp)->
  try
    user = await UserModel.getFromSession {req}
  catch err
    user = null
  addrSuggest {req,user,s:req.body.s},(err,ret)->
    ret ?= {ok:0,err:MSG_STRINGS.INTERNAL_ERROR}
    resp.send ret

# /1.5/props/autocompleteGetNext
POST 'autocompleteGetNext', (req, resp)->
  s = req.body.s
  ts = req.body.ts
  page = req.body.page
  #TODO: get user from callStdFn if stdFn has userObject?
  # req.callStdFn 'addrSearch',{s,ts,req},(err,retObj)->
  try
    user = await UserModel.getFromSession {req}
  catch err
    user = null
  # TODO: @luoxiaowei refactor use rateLimiter
  addrSearch {s,ts,req,user},(err,retObj)->
    return gFnError(resp,err) if err
    if page is 'home'
      retObj.needLogin = true if isReachedLimit {req,user}
    libP.thumbUrlListReplace(retObj.l, req.isChinaIP(), config.share?.hostNameCn)
    return resp.send retObj

MSG_STRINGS.def {EXCEED_QUOTA:'Exceed Maximuim Count'}
POST 'streetautocomplete', (req, resp)->
  # NOTE: cannot test user native if RMPost, apsv cookie was set in get '/app', elsewehere on post no cookie set;
  return gFnError(resp,'Need app') unless req.getDevType() is 'app'
  # TODO: may use our own addrs?
  # TODO: timeout ERR@2021-12-03T13:45:07.202:Request Timeout: 102890 ms ************:realmaster.com:/1.5/props/streetautocomplete@1638557004312
  return gFnError(resp,MSG_STRINGS.BAD_PARAMETER) unless input = req.param 'input'
  UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->
    return gFnError(resp,MSG_STRINGS.NEED_LOGIN) unless user
    if isReachedLimit {req,user}
      return gFnError(resp,MSG_STRINGS.EXCEED_QUOTA)
    
    libPropSearch.getAddrSearchByGoogle {input,isChinaIP:req.isChinaIP()},(err,ret)->
      if err
        debug.error 'streetautocomplete',err
        return gFnError(resp,MSG_STRINGS.NOT_FOUND)
      resp.send ret