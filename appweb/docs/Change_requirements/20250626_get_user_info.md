# 需求 [fix_uuid]

## 反馈

1. 根据jwt返回的用户信息（_id,roles），获取用户其他信息


## 需求提出人:    Rain
## 修改人：       liurui

## 提出日期:      2025-04-01

## 原因

1. 之前的修改被覆盖，重新添加


## 解决办法

1. appauth支持参数 userfields，在function内获取需要的用户信息
2. 修改appauth调用部分,传入需要的参数
3. 修改使用session user的地方改用model function（获取session用户，并获取需要字段）
4. req.user的使用（@user）

## 影响范围

1. app/web GET/POST

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-02

## online-step

1. 重启server





@/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/0_common
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/admin
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/adRelated/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/community/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/condoComplex/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/crm/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/floorPlan/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/group
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/ioNhooks
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/map
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/other
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propNotes
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated


检查 @appweb/src/apps/80_sites/WebRM/ 文件夹下面对appAuth使用的修改是否符合我的要求，是否修改完全，是否检查到了最底层调用的字段
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rltr
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/saveCMA
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/school
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/shortUrl
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/showing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/token
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/transit
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/trustedAssignment
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/weather
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search



/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user



/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM









重新检查,深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/htmltoimg.coffee 文件

我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 291还将user通过参数传入了 searchByIds,inputSearch,addPropAdditionalFields,setUpPropLogin function中， 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop 文件夹下的文件




重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件,文件中getDispVar 有多个地方调用
重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件中所有使用到user的地方
,文件中getDispVar 有多个地方调用

深入分析检查@/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM 文件夹下的文件 appauth 的使用


hasWechat

fullNameOrNickname
sendToUser



检查claim

---

## @/AppRM 文件夹下 req.session.get('user') 修改为 UserModel.getFromSession 统计

/**
 * 修改说明：
 * 以下为 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM 及其子目录下，所有需要将 req.session.get('user')
 * 修改为 await UserModel.getFromSession({req, userfields}, cb) 的代码位置。
 * 每条记录包含文件路径、行号、原代码、修改后代码及所需的 userfields。
 * 确保 UserModel 已正确引入，并根据实际使用的字段最小化 userfields 参数。
 */

1. x_unused/rm1Market.coffee
   - 行241: `# user = @req.session.get('user')`
   - 修改建议：已注释代码，无需修改。

2. x_share/x_apiShare.coffee
   - 行116: `user = req.session.get('user')`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     ```
   - 所需字段：仅使用 user._id，无需额外 userfields

3. x_share/shareDialog.coffee
   - 行85: `# (user = @req.session.get('user')) ...` (已注释，无需修改)
   - 行136: `initVals += 'isLoggedIn='+(@req.session.get('user') isnt null)+';'`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     initVals += 'isLoggedIn='+(user isnt null)+';'
     ```
   - 所需字段：仅判断登录状态，无需额外 userfields

4. user/chat.coffee
   - 行174: `user = req.session.get('user')`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req, userfields: ['eml']}
     ```
   - 所需字段：使用 UserModel.getEmail(user)，需要 ['eml'] 字段

5. user/userPost.coffee
   - 行437: `# suser = req.session.get('user') ...` (已注释，无需修改)
   - 行534: `hasUser:if @req.session.get('user') then true else false,`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     hasUser: if user then true else false,
     ```
   - 行932: `user = req.session.get('user')`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req}
     ```
   - 行1273: `if @req.session.get("user")`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req}
     if user
     ```
   - 所需字段：主要用于判断登录状态和传递给其他函数，无需额外字段

6. stdFunctions/stdFunctions.coffee
   - 行17: `if user = req.session.get('user')`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     if user
     ```
   - 所需字段：仅判断登录状态，无需额外字段

7. x_index/index.coffee
   - 行809: `if user = @req.session.get('user')`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req, userfields: ['avt', 'nm']}
     if user
     ```
   - 行852: `# if user = @req.session.get('user')` (已注释，无需修改)
   - 行966: `# if (user = @req.session.get('user')) ...` (已注释，无需修改)
   - 行973: `if avator = (user = @req.session.get('user'))?.avt`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req, userfields: ['avt', 'nm']}
     if avator = user?.avt
     ```
   - 行1231: `# if user is req.session.get('user') ...` (已注释，无需修改)
   - 所需字段：使用 user.avt 和 user.nm，需要 ['avt', 'nm'] 字段

8. rmListing/rm0Mylisting.coffee
   - 说明：该文件不包含 req.session.get('user') 的直接使用。

9. user/settings.coffee
   - 行621: `suser = req.session.get('user') # update current user`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     suser = await UserModel.getFromSession {req, userfields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId']}
     ```
   - 行686: `if avator = @req.session.get('user')?.avt`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req, userfields: ['avt']}
     if avator = user?.avt
     ```
   - 行721: `if qrcode = @req.session.get('user')?["#{@fld}"]`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req, userfields: [@fld]}
     if qrcode = user?[@fld]
     ```
   - 所需字段：使用多个用户字段，需要相应的字段数组

10. form/formManage.coffee
    - 行60: `unless user = req.session.get('user')`
    - 修改为：
      ```coffeescript
      # 需要引入 UserModel = MODEL 'User'
      user = await UserModel.getFromSession {req}
      unless user
      ```
    - 所需字段：仅判断登录状态，无需额外字段

11. admin/sysAdmin.coffee
    - 说明：该文件不包含 req.session.get('user') 的直接使用，仅使用 realUser 相关功能。

12. 0_common/01_user.coffee
    - 说明：该文件中的 req.session.get('user') 均为注释代码，无需修改。

---

## @/AppRM 文件夹下 req.session.get 'user' 修改为 UserModel.getFromSession 统计

/**
 * 修改说明：
 * 以下为 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM 及其子目录下，所有需要将 req.session.get 'user'
 * （CoffeeScript无括号调用）修改为 await UserModel.getFromSession({req, userfields}, cb) 的代码位置。
 * 每条记录包含文件路径、行号、原代码、修改后代码及所需的 userfields。
 */

1. user/userPost.coffee
   - 行502: `user = req.session.get 'user'`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req, userfields: ['eml']}
     ```
   - 行516: `# user = req.session.get 'user'` (已注释，无需修改)
   - 所需字段：使用 UserModel.getEmail(user)，需要 ['eml'] 字段

2. propRelated/autocomplete.coffee
   - 行200: `user = req.session.get 'user'`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     ```
   - 行212: `user = req.session.get 'user'#retObj.user`
   - 修改为：
     ```coffeescript
     user = await UserModel.getFromSession {req}
     ```
   - 所需字段：主要用于传递给其他函数，无需额外字段

3. auth/index.coffee
   - 行76: `return resp.redirect '/1.5/user/login' unless user = req.session.get 'user'`
   - 修改为：
     ```coffeescript
     # 需要引入 UserModel = MODEL 'User'
     user = await UserModel.getFromSession {req}
     return resp.redirect '/1.5/user/login' unless user
     ```
   - 所需字段：仅判断登录状态，无需额外字段

4. 0_common/01_user.coffee
   - 说明：该文件中的 req.session.get 'user' 均为注释代码，无需修改。

---

## 更新说明（2025-07-10）

- 移除了 `req.session.get('realUser')` 相关的统计，因为 realUser 不在此次统计范围内
- 更新了部分行号以反映代码的实际位置
- 确认统计涵盖了所有 `req.session.get('user')` 和 `req.session.get 'user'` 的使用位置
- 统计结果经过实际代码验证，准确性得到确认