UserModel = MODEL 'User'

###
VIEW 'shareDialog',->
  div id:'shareDialog',->
    div id: 'backdrop', class: 'backdrop', style: 'display:none;'
    navParam = ''
    if @height
      navParam = style:"height:#{@height}"
    if @publishNews
      navParam = style:"height:450px"

    nav class: 'menu slide-menu-bottom smb-lg', ngClass:"", id:'share-content', navParam,->
      #text 'debug test'
      div class: 'content-padded', style:'  margin: 10px;',->
        ul class: 'table-view', ->
          #if true#@showPromoteWeshare
          unless @hideIshare
            li class: 'table-view-cell', style:"border-bottom: none; font-size:15px;padding:0px 0px 0px 0px;text-align:left;box-shadow:1px 1px 5px #888;background: #EF564B;", ->
              a class:'', style:"display:block; margin:0", href:"#", onclick:"RMSrv.share('i-share',#{@doc or 'null'});", ->
                div style:"vertical-align: top; height: 82px; background: #EF5953; overflow: hidden; ",-> #eee
                  div style:"width: 82px; display:inline-block;  vertical-align: top; padding: 19px 10px 0 17px;", class:'',->
                    i class:'fa fa-paper-plane-o', style:"font-size:41px; color:white;"
                  div style:"display: inline-block; width: calc(100% - 89px);   color: white; padding-top: 10px; line-height: 25px;",->
                    text _ "iSharing"
                    p style:" line-height: 1.2em; font-size: 13px; color:white",->
                      text _ "WeChat advertisement service, let more people see your promotion."

          # if @showPromote3P
          #   li class: 'table-view-cell', style:"font-size:15px;padding:0px 0px 0px 0px; height: 100px; text-align: left;", ngShow:"prop.bcf !== 'b'",->
          #     a class:'', style:"display:block; margin:0", ngHref:"/1.5/promote/mylisting?ml_num={{prop.ml_num}}",->
          #       div style:"vertical-align: top; height: 82px; background: #eee; overflow: hidden; ",->
          #         img src:"/img/share_3p.png",class:"pull-left", style:"min-width: 82px;"
          #         div style:"padding-left: 89px; height: 50px",->
          #           text "Promote Your Listing"
          #           p style:" line-height: 1.1em; font-size: 13px;",->
          #             text "Your Listings can be easily promoted to China Market"
          #         div ->
          #           img class:"pull-right", style:"width: 28px; margin-right:5px", src:"/img/3p_UHouzz.png"
          #           img class:"pull-right", style:"width: 28px; margin-right:5px", src:"/img/3p_58.png"
          #           img class:"pull-right", style:"width: 28px; margin-right:5px", src:"/img/3p_RMLMarket.png"

          if @publishNews
            liParam = style:'font-size:15px;padding:10px 0px; padding-bottom:3px; border-bottom:1px none;'
          else
            liParam = style:'font-size:15px;padding:10px 0px; padding-bottom:20px;'
          li class: 'table-view-cell', align: 'center', liParam, ->
            div class: 'float-left', onclick:"RMSrv.share('wechat-moment',#{@doc or 'null'});",ngClick:"resize('l'); rmShare('wechat-moment', #{@doc or 'null'})",->
              img src:'/img/share_wechat1.png'
              br()
              text _ 'Wechat Moment'
            div class: 'float-left', onclick:"RMSrv.share('wechat-friend',#{@doc or 'null'});",ngClick:"resize('l'); rmShare('wechat-friend', #{@doc or 'null'})",->
              img src:'/img/share_wechat2.png'
              br()
              text _ 'Wechat Friend'
            div class: 'float-left', onclick:"RMSrv.share('facebook-feed',#{@doc or 'null'});",ngClick:"resize('l'); rmShare('facebook-feed', #{@doc or 'null'})",->
              img src:'/img/share_facebook.png'
              br()
              text _ 'Facebook'
            div class: 'float-left', onclick:"RMSrv.share('qr-code',#{@doc or 'null'});",ngClick:"resize('l'); rmShare('qr-code', #{@doc or 'null'})",->
              img src:'/img/share_qrcode.png'
              br()
              text _ 'QR-Code'
            div class: 'float-left', onclick:"RMSrv.share('other',#{@doc or 'null'});",ngClick:"resize('l'); rmShare('other', #{@doc or 'null'})",->
              img src:'/img/share_more.png'
              br()
              text _ 'More'

          if @publishNews
            li class: 'table-view-cell', align: 'center', style:'font-size:15px;padding:10px 0px; padding-bottom:10px;',->
              div class: 'float-left', id:'shareToNews',->
                img src:'/img/share_news.png'
                br()
                text _ 'Forum'

          li class: 'table-view-cell', align: 'center', style:'padding: 15px 0 10px 0;',->
            # Title & Descript
            div ->
              label ->
                text _ "Title","share"
              input id:'id_share_title',class:'share-title',placeholder:_('Default Title'),ngFocus:"hide()"
            div ->
              label ->
                text _ "Desc","share"
              textarea id:'id_share_desc',class:'share-desc',placeholder:_('Default Description'),ngFocus:"hide()"

            div class:'promoWrapper',->
              if (not @noSign)  #(user = @req.session.get('user')) and (user.roles?.indexOf('realtor') >= 0)
                inputParam = class:'disabled'
                labelParma = style:'display:none'
                if (@req.isAllowed 'shareSignProp') or @showSign
                  inputParam = ""
                  labelParma = ""
                  div class:'id_with_sign_wrapper',->
                    label for:'id_with_sign', id:'id_with_sign',class:'pull-left',ngShow:'prop || blog || toShare', labelParma, ->
                      input type:'checkbox',ngModel:'wSign',checked:true, inputParam
                      text ' '
                      text _ "Signature"
                if @forceDownload
                  inputParam = class:'disabled'
                  labelParma = style:'display:none'
                div class:'id_with_dl_wrapper', ngInit:"wDl = true",->
                  label for:'id_with_dl', id:'id_with_dl',class:'pull-left',ngShow:'prop || blog || toShare', labelParma, ->
                    input type:'checkbox',ngModel:'wDl',checked:true, inputParam
                    text ' '
                    text _ "Personal Promo"
                div class:'lang-selectors-wrapper', ->
                  unless ('en' is (locale = @req.locale())) or @noLang
                    div class: 'segmented-control lang-selectors', ->
                      a class: 'control-item lang-selector',id:'id_share_lang_en',onclick:"RMSrv.share('lang-en');",href:"javascript:void(0)",-> 'En'
                      a class: 'control-item lang-selector active',id:'id_share_lang_cur',onclick:"RMSrv.share('lang-cur');",href:"javascript:void(0)",->
                        text if locale is 'zh' then '繁' else if locale is 'zh-cn' then '中' else _('Current','language')
          li class: 'table-view-cell cancel-li', style:'padding:5px 0 10px 0;',->
            a href:"javascript:void 0",style: 'margin: 0px 10px 0px 10px; padding-top: 10px; padding-left:0;',
            onclick: "RMSrv.share('hide');", ngClick:"resize('l')", -> _ 'Cancel'

    div id:'id_share_qrcode',class:'pic',style:"margin:0 0; position:absolute; bottom:0px; z-index:30;
     width: 100%;padding: 5px; text-align:center; display: inline-block; vertical-align: top;
     background-color: #fff;display:none;",->
      div id:'id_share_qrcode_holder'
      br()
      div style: 'border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;'
      button class:'btn btn-block btn-long',onclick:"RMSrv.share('qr-code-close');", style:'border:1px none;', ->
        text _ 'Close'
###

###
  angular seems not working in this page.....
  if need to fix wepageEdit related, please fix it in wepageEdit.coffee
###
VIEW 'shareDialog2',->
  css '/css/sprite.min.css'
  page = @page or 'dl'
  initVals = ""
  shareLinks = @req.shareLinkStrategy(page, @req)
  initVals += 'dispList='+JSON.stringify(shareLinks?.l)+';'
  initVals += 'dispAllow='+JSON.stringify(shareLinks?.v)+';'
  initVals += 'locale=\''+@req.locale()+'\';'
  # 使用传入的 isLoggedIn 参数，如果没有传入则通过 UserModel.getFromSession 获取
  if @isLoggedIn?
    isLoggedIn = @isLoggedIn
  else
    try
      user = await UserModel.getFromSession {req: @req}
      isLoggedIn = user isnt null
    catch err
      isLoggedIn = false
  initVals += 'isLoggedIn='+isLoggedIn+';'
  initVals += 'isVipUser='+(@req.isAllowed('vipUser'))+';'
  initVals += 'isRealtor='+(@req.isAllowed('brokerage'))+';'
  permi = if page is 'wecard' then true else @req.isAllowed('shareSignProp');
  initVals += 'allowedShareSignProp='+permi+';'
  initVals += 'noSign='+(if @noSign then 'true' else 'false')+';'
  initVals += 'noLang='+(if @noLang then 'true' else 'false')+';'
  initVals += 'hideSplit='+(if @hideSplit then 'true' else 'false')+';'
  initVals += 'wDlDisable='+(if (@req.isAllowed('brokerage') and not @req.isAllowed('vipRealtor')) then 'true' else 'false')+';'
  initVals += 'publishNews='+(@req.isAllowed('newsAdmin') or @req.isAllowed('vipAlliance'))+';'
  div id:'shareDialog', ngInit:initVals, ->
    div class:'backdrop', id:'backdrop', style:"display:none", ->
    nav class:'menu slide-menu-bottom smb-md', style:"{height:height}", ->
      div class:'first-row', ->
        div onclick:"RMSrv.share('wechat-moment',#{@doc or 'null'})", ->
          span class:'sprite50-52 sprite50-5-3'
          div class:'inline', ->
            text _ "Wechat Moment"
        div onclick:"RMSrv.share('wechat-friend',#{@doc or 'null'})", ->
          span class:'sprite50-52 sprite50-5-2'
          div class:'inline', ->
            text _ "Wechat Friend"
        div onclick:"RMSrv.share('facebook-feed',#{@doc or 'null'})", ->
          span class:'sprite50-52 sprite50-5-4'
          div class:'inline', ->
            text _ "Facebook"
        div onclick:"RMSrv.share('qr-code',#{@doc or 'null'})", ->
          span class:'sprite50-52 sprite50-6-1'
          div class:'inline', ->
            text _ "QR-Code"
        div onclick:"RMSrv.share('other',#{@doc or 'null'})", ->
          span class:'sprite50-52 sprite50-5-5'
          div class:'inline', ->
            text _ "More"
      div class:'split', ngHide:"dispList.toString() == '' || hideSplit", ->
        div class:'left inline'
        div class:'text inline', ->
          span -> _ "Advanced Features"
          # span -> "-"
          # span class:'vip', ->
          #   text _ "VIP Only"
        div class:'right inline'
      div class:'second-row', ->
        div id:'shareToNews', ngShow:"publishNews && dispList.indexOf('news')>-1",->
          span class:'sprite50-52 sprite50-6-3'
          div class:'inline',->
            text _ "News"
        div class:'58', ngClick:"promote('58', formData);", ngShow:"dispList.indexOf('58')>-1",->
          span class:'sprite50-52 sprite50-4-4'
          div class:'inline',->
            text  "58.com"
        div class:'market', ngClick:"promote('market', formData);", ngShow:"dispList.indexOf('market')>-1",->
          span class:'sprite50-52 sprite50-4-2'
          div class:'inline',->
            text _ "Listing Market"
        div class:'vt',ngClick:"createWePage('vt')",ngShow:"dispList.indexOf('vt')>-1",->
          span class:'sprite50-52 sprite50-4-3'
          div class:'inline',->
            text _ "WePage Flyer"
        div class:'blog',ngClick:"createWePage('blog')",ngShow:"dispList.indexOf('blog')>-1",->
          span class:'sprite50-52 sprite50-4-5'
          div class:'inline',->
            text _ "WePage Blog"
      div class:'cancel',->
        div class:'promoWrapper', ngClass:"{hidden: noSign}", ->
          div class:'inline' ,id:'id_with_sign_wrapper', ngShow:'allowedShareSignProp',->
            label id:'id_with_sign',->
              input type:'checkbox', ngModel:'wSign', checked:'true', ->
              text _ "Signature"
          div class:'inline', id:'id_with_dl_wrapper',->
            label id:'id_with_dl', ngShow:"isLoggedIn && isVipUser",->
              input type:'checkbox', ngModel:'wDl', checked:'true', ngClass:"{disabled:wDlDisable}"
              text _ "Promo"
        div class:'lang-selectors-wrapper', ngClass:"{hidden: locale == 'en' || noLang}",->
          div class:'segmented-control lang-selectors', ->
            a id:'id_share_lang_en', class:'control-item lang-selector', onclick:"RMSrv.share('lang-en');", href:"javascript:;", ->
              text "En"
            a id:'id_share_lang_cur', class:'control-item lang-selector active', onclick:"RMSrv.share('lang-cur');", href:"javascript:;", ->
              span ngShow:"locale == 'zh'", -> text "繁"
              span ngShow:"locale == 'zh-cn'", -> text "中"
              span ngShow:"locale == 'kr'", -> text "한"
              span ngShow:"locale !== 'zh' && locale !== 'zh-cn' && locale !== 'kr'", -> text _ "Current","language"
        a class:'cancel-btn', href:"javascript:;", onclick:"RMSrv.share('hide');", -> text _ "Cancel"

    div id:'id_share_qrcode', class:'pic', ->
      div id:'id_share_qrcode_holder'
      br()
      div style:"border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;"
      button class:'btn btn-block btn-long', onclick:"RMSrv.share('qr-code-close');", style:"border:1px none;",->
        text _ "Close"

###
VIEW 'toBeUnlocked',->
  nav class: 'menu slide-menu-bottom smb-md',id:'share-content', style:'height:185px;',->
    div class: 'content-padded', ->
      ul class: 'table-view', ->
        li class: 'table-view-cell', style:'font-size:15px; height:135px; padding:15px 0 0 0;',->
          div style:' text-align: left;  padding: 0 18px;',->
            text _ "Inviting a realtor to use RealMaster App, unlock more fabulous features."
          a class:"btn btn-block btn-long btn-positive", href:'/1.5/user/unlock',style:'color:white;',-> _ "Unlock this feature!"
        li class: 'table-view-cell cancel-li', style:'padding-top:0;',->
          div ->
            a class: 'btn btn-long btn-block ', onclick: "RMSrv.share('hide');", style:'border:1px none; position:relative;',-> _ 'Cancel'
###
