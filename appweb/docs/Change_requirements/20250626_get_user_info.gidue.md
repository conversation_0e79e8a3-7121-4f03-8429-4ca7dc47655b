# appAuth 函数用户字段优化修改要求

## 修改目标
将 `appAuth` 函数调用从返回完整用户对象改为只返回必要的用户字段，以提高安全性和性能。

## 核心修改规则

### 1. 基本原则
- **只添加实际使用到的字段**：不要添加未使用的字段
- **深入分析函数调用链**：不仅要检查直接使用的字段，还要追踪通过函数调用间接使用的字段
- **默认字段无需指定**：`_id` 和 `roles` 字段是默认返回的，无需在 `userfields` 中指定

### 2. 分析方法

#### 直接字段使用
检查 `appAuth` 回调函数中直接使用的 user 字段：
```coffeescript
# 直接使用示例
user._id          # 默认字段，无需指定
user.eml          # 需要添加到 userfields
user.nm           # 需要添加到 userfields
user.hasWechat    # 需要添加到 userfields
```

#### 深层函数调用链分析
**必须追踪所有将 user 对象作为参数传递的函数调用**：

1. **函数参数传递**：
```coffeescript
someFunction(user)           # 需要检查 someFunction 使用了哪些 user 字段
anotherFunction({user, ...}) # 需要检查 anotherFunction 使用了哪些 user 字段
```

2. **多层调用链**：
```coffeescript
# 示例：appAuth -> getContactRealtor -> getUserFollowStatus
# 需要追踪到 getUserFollowStatus 函数使用的 user.flwngRm, user.flwng 字段
```

3. **错误日志记录**：
```coffeescript
# 错误日志中使用的字段也需要包含
debug.error {uid:user._id, eml:user.eml}  # 需要 eml 字段
```

### 3. 修改语法
```coffeescript
# 修改前
UserModel.appAuth {req,resp}, (user) ->

# 修改后（单个字段）
UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->

# 修改后（多个字段）
UserModel.appAuth {req,resp,userfields:['eml','nm','hasWechat']}, (user) ->
```

### 4. 不需要修改的情况

1. **只做权限检查**：
```coffeescript
UserModel.appAuth {req,resp}, (user) ->
  return error unless req.hasRole('admin')  # 只使用 roles，无需修改
```

2. **只检查用户是否存在**：
```coffeescript
UserModel.appAuth {req,resp}, (user) ->
  return error unless user  # 只检查存在性，无需修改
```

3. **只使用默认字段**：
```coffeescript
UserModel.appAuth {req,resp}, (user) ->
  query = {uid: user._id}  # 只使用 _id，无需修改
```

### 5. 常见字段使用场景

- **`eml`**：错误日志记录、通知发送
- **`nm`, `nm_zh`, `nm_en`**：用户名显示、日志记录
- **`hasWechat`**：微信功能控制
- **`flwng`, `flwngRm`**：用户关注状态检查
- **`city`, `cities`**：地理位置相关功能
- **`avt`**：头像显示

### 6. 分析工具和方法

1. **搜索直接使用**：
```regex
user\._id|user\.eml|user\.[a-zA-Z]
```

2. **搜索函数调用**：
```regex
user[,\)]
```

3. **检查函数定义**：使用 `codebase-retrieval` 工具查找函数定义和实现

### 7. 质量保证

- **逐个文件检查**：不要跳过任何文件
- **验证修改正确性**：确保添加的字段确实被使用
- **避免过度添加**：不要添加"可能用到"但实际未使用的字段
- **考虑向后兼容**：确保修改不会破坏现有功能

### 8. 统一 session user 获取方式

为保证 user 字段安全与一致性，所有通过 `req.session.get('user')` 获取用户数据的地方，必须统一改为调用 `UserModel.getFromSession({req, userfields}, cb)` 方法。

#### 具体要求
- **禁止直接使用**：不允许直接通过 `req.session.get('user')` 获取 user 对象。
- **必须使用 getFromSession**：统一调用 `UserModel.getFromSession({req, userfields}, cb)`，由该方法根据 userfields 参数决定返回哪些字段。
- **默认字段**：如未指定 userfields，仅返回 `_id` 和 `roles` 字段（即 session 中原始 user）。
- **按需返回**：如需其它字段，必须通过 userfields 明确指定（如 `['eml','nm']`），或在极少数场景使用 `'all'`，并遵循字段最小化原则。
- **回调用法**：getFromSession 的回调参数为 user 对象（或 null），后续所有 user 字段访问均应基于该回调结果。

#### 示例
```coffeescript
# 错误用法（禁止）
user = req.session.get('user')

# 正确用法（只需 _id/roles）
UserModel.getFromSession {req}, (user) ->
  # 只可访问 user._id, user.roles

# 正确用法（需其它字段）
UserModel.getFromSession {req, userfields:['eml','nm']}, (user) ->
  # 可安全访问 user.eml, user.nm
```

#### 说明
- getFromSession 会自动处理 session user 的字段补全与安全过滤，避免因 session 残留导致的字段泄漏。
- 该方法与 appAuth 字段精简规范一致，递归分析 userfields 需求，确保最小权限原则。
- 极少数需要完整 user 信息的场景，允许 userfields:'all'，但需有充分业务理由。

> **注意：**
> - 项目中存在两种 session user 获取写法：`req.session.get('user')` 和 `req.session.get 'user'`，二者等价。
> - **无论哪种写法，均必须统一替换为 `UserModel.getFromSession({req, userfields}, cb)`。**
> - 替换时，需递归分析所有 user 字段的直接和间接（多层函数调用、对象传递、日志、模板字符串等）使用，确保 userfields 字段最小化、无遗漏、无冗余。

## 示例对比

### 错误示例
```coffeescript
# 错误：添加了未使用的字段
UserModel.appAuth {req,resp,userfields:['city','cities']}, (user) ->
  if isReachedLimit {req,user}  # 实际只使用了 user.eml
    return error
```

### 正确示例
```coffeescript
# 正确：只添加实际使用的字段
UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->
  if isReachedLimit {req,user}  # isReachedLimit 函数使用 user.eml
    return error
```

## 深层函数调用链分析示例

### 示例1：错误日志记录
```coffeescript
# appAuth 调用
UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->
  if someCondition
    debug.error {uid:user._id, eml:user.eml}  # 使用 eml 字段
```

### 示例2：多层函数调用
```coffeescript
# appAuth -> getContactRealtor -> getUserFollowStatus
UserModel.appAuth {req,resp,userfields:['flwngRm','flwng']}, (user) ->
  result = getContactRealtor({user, ...})  # 内部调用 getUserFollowStatus(user)
```

### 示例3：属性检查
```coffeescript
# appAuth 调用
UserModel.appAuth {req,resp,userfields:['hasWechat']}, (user) ->
  hasWechat = UserModel.hasWechat(user, {hasWechat})  # 使用 user.hasWechat
```

## 修改流程

1. **定位 appAuth 调用**：搜索所有 `appAuth` 调用
2. **分析直接使用**：检查回调函数中直接使用的 user 字段
3. **追踪函数调用**：找到所有将 user 作为参数的函数调用
4. **深入函数定义**：使用工具查找函数定义，分析使用的字段
5. **收集所需字段**：汇总所有直接和间接使用的字段
6. **应用修改**：添加 `userfields` 参数，只包含必要字段
7. **验证修改**：确保修改正确且不遗漏字段

## 注意事项

- **函数调用链可能很深**：需要递归分析多层调用
- **条件使用**：注意 `user?.field` 这种条件使用的字段
- **对象传递**：注意 `{user, ...}` 这种对象传递方式
- **数组访问**：注意 `user.flwng[0]?.uid` 这种数组访问
- **字符串模板**：注意模板字符串中使用的字段

这个修改逻辑的核心是**精确性**和**完整性**：既要确保包含所有需要的字段，又要避免包含不必要的字段。