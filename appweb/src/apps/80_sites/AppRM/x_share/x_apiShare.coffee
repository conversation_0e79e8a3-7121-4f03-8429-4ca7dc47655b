###
URL: /mp/encrypedData.md5Sum
load key from config

Sign:
  d = data = encrypt(data)
  s = sum  = createSum(data)
Verify:
  data = decrypt(data)
  s = createSum(data)?

just encrypt id and to, dont care user name and download
set viewed in cookie, check cookie, 24hr
promoteListing58 add surl(share url)

filter download in template, load use ajax

use new layout, without cordova, just angular or jquery(one of)
统计访问信息
vc
vcd
vcwb //58
vcdz //ishare
vcyx //uhouzz
sc #share counter
scd

+ collection
key: unify addr
mlsid: [id1,id2 ...]

###
config = CONFIG(['share'])
debug = DEBUG()

Properties = MODEL 'Properties'
ProjectModel = MODEL 'Project'
UserModel = MODEL 'User'
ShortUrl = MODEL 'ShortUrl'

# UserListings = COLLECTION 'chome','user_listing'
Wecard = COLLECTION 'chome','wecard'

# helpers = INCLUDE 'lib.helpers'
checksum = INCLUDE 'lib.checksum'
libProperties = INCLUDE 'libapp.properties'

# getExchangeRates = DEF 'getExchangeRates'
# getExchangeNames = DEF 'getExchangeNames'

process_listing_req     = DEF 'process_listing_req'
process_listingList_req = DEF 'process_listingList_req'
# process_mylisting_req   = DEF 'process_mylisting_req'
process_wecard_req      = DEF 'process_wecard_req'
# fnIsRealtor = DEF 'isRealtor'

# 短url分享
# 单个房源，房源列表，微图文（wecard）
# showing，新闻（News，cfg cip改为false）未调用shareInfo接口
# 以下保留现有分享逻辑
# CMA（目前图片），估值，微网站，楼花，经纪/商家

APP '1.5'
APP 'api', true
APP 'rm', true

# NOTE: rmsrv5wk wont post to this api if page has no share-data
POST 'shareInfo',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    # decode string and generate url
    #id=RM1&dl=1
    data = {share:'1'}
    error = (err)->
      return resp.send {ok:0, err:err}
    # ownerid is ownerid form rmlisting
    channel = req.param('channel')
    for i in ['id','tp','to','wDl','uid','aid','ownerid','lang','sgn','dl','wSign']
      data[i] = req.param i if req.param i
    return error('No id or type Info') unless data.tp and data.id

    data['to'] = libProperties.generate_shared_to_from_share data['to']
    propId = req.param 'id'
    devType = req.getDevType()
    
    surlData = {data, uid: user?._id, channel}
    if data.tp is 'wecard'
      surlData.type = 'shareWecard'
    else
      surlData.type = 'shareProp'

    try
      shortUrlId = await ShortUrl.create surlData
    catch err
      debug.error 'create short_url error:',err,'crete data:',surlData
      return resp.send {ok:0,err}
    # TODO: fix this is pre share
    # NOTE: wecard dont inc stats for prop
    if data.tp isnt 'wecard'
      try
        await Properties.incListingShareStatsAsync {id:propId, devType, user}
      catch err
        debug.error 'increase ListingShareStats error:',err,"propId:#{propId},devType:#{devType},uid:#{user._id}"
        return resp.send {ok:0,err}
    url = "#{req.shareHost()}/s/#{shortUrlId}?lang=#{req.param('lang') or 'en'}"
    resp.send {ok:1, url}


APP 'mp'

getPrefix = (req)->
  if req.param 'pre'
    return 'shrp'
  return 'shr'

update_mylisting_stats = (req,resp, message, cb) -> #pass
  pre = req.param 'pre'
  try
    user = await UserModel.getFromSession {req}
  catch err
    return cb(err)
  devType = req.getDevType()
  Properties.incListingStats {ids:[message?.r],pre,user,devType},cb
  # return cb('No id') unless message.r
  # inc = getIncObj(req)
  # UserListings.updateOne {id: message.r}, {$inc:inc}, {upsert:false,returnDocument:'after'}, (err)->
  #   return cb('Not Updated') if err
  #   return cb null, 'updated'
update_listing_stats = (req,resp, message, cb) -> #pass
  UserModel.appAuth {req,resp},(user)->
    pre = req.param 'pre'
    devType = req.getDevType()
    Properties.incListingStats {ids:[message?.r],pre,user,devType},cb

update_proj_stats = (req,resp, message, cb) -> #pass
  return cb('No id') unless message.r
  UserModel.appAuth {req,resp},(user)->
    pre = req.param 'pre'
    ProjectModel.incProjStats {ids:[message.r], pre, user}, (err,ret)->
      return cb('Not Updated') if err
      return cb null, 'done'
update_listingList_stats = (req,resp, message, cb) ->
  #update when get propDetail JSON
  pre = req.param 'pre'
  # message.r = 'RM1-1234,TRBW1212'
  devType = req.getDevType()
  Properties.incListingStats {ids:message?.r,pre,user:{},devType},cb

update_wecard_stats = (req,resp, message, cb) -> #pass
  return cb('No id') unless message.r
  inc = {}
  prefix = getPrefix(req)
  inc['meta.'+prefix] = 1
  inc['meta.'+prefix+'d'] = 1
  Wecard.updateOne {_id: message.r},{$inc:inc},{upsert:false,returnDocument:'after'},(err)->
    return cb('Not Updated') if err
    return cb null, 'done'
# update_event_stats = (req,resp, evtid) ->
#   return error('No tpl') unless evtid
#   lang = req.locale()
#   Wecard.updateOne {tp:'evtad', _id:evtid},{$inc:{"meta.shr": 1, "meta.shrd": 1}},{upsert:false,returnDocument:'after'},(err)->
#     return error('Not Updated') if err
#     return resp.send {ok:1}

# TODO: when factor code figure a better way to do the stat
process_listing_map =
  # M:process_mylisting_req
  L:process_listing_req
  T:process_listingList_req
  W:process_wecard_req
update_listing_stats_map =
  M:update_mylisting_stats
  L:update_listing_stats
  T:update_listingList_stats
  W:update_wecard_stats
  P:update_proj_stats

SURL_PATTERN = /[0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz]{10}/
verify_e_data = (data)->
  if 'string' is typeof data
    # ec
    if (/^(proj:)?[a-f0-9]{10,}.*$/.test data)
      return true
    # surl
    if SURL_PATTERN.test data
      return true
  return false
  

#need resp in appAuth
updatePropSharedInfo = (req, resp, cb)->
  data = req.param 'data'
  # NOTE: pre means clicked share before user click share to(before invoke 3rd party share)
  preShare = req.param 'pre'
  # shr -> share count
  # shrd -> daily shared
  # decode and get data
  return cb('NOT_VALID_DATA') unless verify_e_data(data)
  #TODO: support for old versions
  if ('string' is typeof data) and /^proj:[a-f0-9]{24}$/i.test data
    message =
      e: 'P'
      r: data.split(':')[1]
  else if ('string' is typeof data) and /^[a-f0-9]{24}$/.test data
    message =
      e: 'W'
      r: data
  else if ('string' is typeof data) and SURL_PATTERN.test data
    try
      # NOTE: dont care about preShare，因为用户分享后不一定会返回房大师页面，现在分享就是7天有效期，无论成功与否
      # 假如用户分享后没有返回房大师页面那么就调不到回调接口，导致无法统计
      # if not preShare
      await ShortUrl.extendExpWhenShareSuccess data
    catch err
      debug.error "extendExpWhenShareSuccess error, shortUrl_id:#{data}, err:",err
      return cb null,MSG_STRINGS.DB_ERROR
    message =
      e: 'L'
      r: data
    # NOTE: surl has its own stat, no stat for preshare and share
    return cb null,'NO STAT FOR NOW'
  else
    secret = config.share?.secret
    [pass, message] = checksum.shareDecode(data,secret)
    return cb('CANT_DECODE') unless pass
    try
      message = JSON.parse message
    catch e
      return cb('Invalid Data')
  if fn = update_listing_stats_map[message.e]
    return fn req,resp,message, (err, ret)->
      cb err,ret
  console.log 'unknown media type'
  cb('unknown type of media page')
DEF 'updatePropSharedInfo',updatePropSharedInfo

POST 'shared', (req, resp)->
  error = (err)->
    return resp.send {ok:0, err:err}
  updatePropSharedInfo req, resp, (err, ret)->
    # console.error err
    return error(err) if err
    resp.send {ok:1}

GET ':data', (req,resp)->
  data = req.param 'data'

  cfg =
    hasU:       1
    avt:        "/img/icon_Alert.png"
    nm:       req.l10n 'RealMaster'
    cardUrl:  req.fullUrl('/getapp')
    noWeCard: 1
    err:      1
    noLoading:  1
    shareInfoText:  req.l10n "Can not find the page. Please check the URL and try again."

  error = ()->
    return resp.ckup 'NewsMiddleAdPageEn', cfg, 'adPage', {jump:0}
  return error() unless verify_e_data(data) #(/^([\w]+=[\w\-]+&?)+$/.test data))

  secret = config.share?.secret
  [pass, message] = checksum.shareDecode(data,secret)
  # TODO: change "e":"M" -> "E":"L"??
  return error() unless pass

  try
    message = JSON.parse message
  catch e
    console.log 'Invalid Data'
    return error()

  fn = process_listing_map[message.e]
  return fn({req,resp,message}) if fn
  console.log 'unknown type of media page'
  return error()

POST 'decData', (req, resp)->
  ec = req.body.ec
  error = ()->
    resp.send {ok:0, e:'Invalid', url:'/1.5/prop/error', ec:2}
  return error() unless verify_e_data(ec)
  secret = config.share?.secret
  [pass, message] = checksum.shareDecode(ec,secret)
  return error() unless pass
  try
    message = JSON.parse message
  catch e
    console.log 'Invalid Data'
    return error()
  # TODO: message.a = data.uid supp?
  resp.send {id:message.r, tp:message.e}

APP '1.5'
APP 'user',true
# updateUserActivity = DEF 'updateUserActivity'
# update user active points #活跃度/ rmb (RealMaster 币)
# based on user action instead of instructions of what to do
# /1.5/user/update?act=share&data=xxxx&pre=1
POST 'update',(req,resp)->
  done = ()->
    resp.send {ok:1}
  UserModel.appAuth {req,resp},(user)->
    # get user action, based on action we decide what to do
    # TODO: may consider encrypt action?
    action = req.param 'act'
    updatePropSharedInfo req,resp,(err,ret)->
      if err then return resp.send {err:err}
      if user
        UserModel.incActivity {req, user, action}, (err, nUser)->
          if err then return resp.send {err:err}
          done()
      else
        done()
