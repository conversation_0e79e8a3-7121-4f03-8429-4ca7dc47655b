# 需求 [fix_uuid]

## 反馈

1. 根据jwt返回的用户信息（_id,roles），获取用户其他信息


## 需求提出人:    Rain
## 修改人：       liurui

## 提出日期:      2025-04-01

## 原因

1. 之前的修改被覆盖，重新添加


## 解决办法

1. appauth支持参数 userfields，在function内获取需要的用户信息
2. 修改appauth调用部分,传入需要的参数
3. 修改使用session user的地方改用model function（获取session用户，并获取需要字段）
4. req.user的使用（@user）

## 影响范围

1. app/web GET/POST

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-02

## online-step

1. 重启server





@/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/0_common
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/admin
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/adRelated/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/community/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/condoComplex/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/crm/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/floorPlan/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/group
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/ioNhooks
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/map
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/other
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propNotes
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated


检查 @appweb/src/apps/80_sites/WebRM/ 文件夹下面对appAuth使用的修改是否符合我的要求，是否修改完全，是否检查到了最底层调用的字段
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rltr
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/saveCMA
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/school
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/shortUrl
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/showing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/token
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/transit
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/trustedAssignment
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/weather
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search



/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user



/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM









重新检查,深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/htmltoimg.coffee 文件

我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 291还将user通过参数传入了 searchByIds,inputSearch,addPropAdditionalFields,setUpPropLogin function中， 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop 文件夹下的文件




重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件,文件中getDispVar 有多个地方调用
重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件中所有使用到user的地方
,文件中getDispVar 有多个地方调用

深入分析检查@/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM 文件夹下的文件 appauth 的使用


hasWechat

fullNameOrNickname
sendToUser



检查claim

---

## @/AppRM 文件夹下 req.session.get('user') 使用统计（截至2025-07-02）

/**
 * 统计说明：
 * 以下为 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM 及其子目录下，所有通过 req.session.get('user') 获取用户信息的代码位置。
 * 每条记录包含文件路径、行号及简要说明。
 * 便于后续代码重构、权限审查及用户信息获取方式统一。
 */

1. x_unused/rm1Market.coffee
   - 行241: `# user = @req.session.get('user')`
   - 说明：已注释，疑似历史遗留代码。

2. x_share/x_apiShare.coffee
   - 行116: `user = req.session.get('user')`
   - 说明：获取当前会话用户，通常用于分享相关接口鉴权。

3. x_share/shareDialog.coffee
   - 行85: `# (user = @req.session.get('user')) ...`
   - 行136: `initVals += 'isLoggedIn='+(@req.session.get('user') isnt null)+';'`
   - 说明：判断用户是否登录，控制分享弹窗内容。

4. user/chat.coffee
   - 行174: `user = req.session.get('user')`
   - 说明：获取当前用户信息，用于聊天功能。

5. user/userPost.coffee
   - 行436: `# suser = req.session.get('user') ...`
   - 行533: `hasUser:if @req.session.get('user') then true else false,`
   - 行931: `user = req.session.get('user')`
   - 行1272: `if @req.session.get("user")`
   - 行1410: `return resp.send {e:'Do not update pn if switch user'} if req.session.get('realUser')`
   - 说明：涉及用户发帖、权限判断、用户状态检测等。

6. stdFunctions/stdFunctions.coffee
   - 行17: `if user = req.session.get('user')`
   - 说明：通用函数，判断用户是否登录。

7. x_index/index.coffee
   - 行808: `if user = @req.session.get('user')`
   - 行851: `# if user = @req.session.get('user')`
   - 行965: `# if (user = @req.session.get('user')) ...`
   - 行972: `if avator = (user = @req.session.get('user'))?.avt`
   - 行1230: `# if user is req.session.get('user') ...`
   - 说明：用于首页展示、头像获取、用户相关展示逻辑。

8. rmListing/rm0Mylisting.coffee
   - 行76: `realUser = (req.session.get('realUser') or user)`
   - 说明：获取真实用户信息，常用于多用户切换场景。

9. user/settings.coffee
   - 行620: `suser = req.session.get('user') # update current user`
   - 行685: `if avator = @req.session.get('user')?.avt`
   - 行720: `if qrcode = @req.session.get('user')?["#{@fld}"]`
   - 说明：用户设置页，获取头像、二维码等信息。

10. form/formManage.coffee
    - 行59: `unless user = req.session.get('user')`
    - 说明：表单管理，校验用户登录状态。

11. admin/sysAdmin.coffee
    - 行344: `user = (req.session.get('realUser') or user)`
    - 行545: `realUser = (req.session.get('realUser') or user)`
    - 说明：系统管理，涉及真实用户与切换用户。

12. 0_common/01_user.coffee
    - 行106: `# user = req.session.get('user')`
    - 行223: `# if user is req.session.get('user') ...`
    - 行347: `# #unless user = req.session.get('user')`
    - 行579: `# if user = req.session.get('user')`
    - 行625: `# if (suser = req.session.get('user')) ...`
    - 行678: `# if user = req.session.get('user')`
    - 行728: `# return cb {ok:0,err:"Unknown User"} unless user = (req.session.get('realUser') or req.session.get('user'))`
    - 行732: `# if not (realUser = req.session.get('realUser'))`
    - 行750: `# unless req.session.get('realUser')`
    - 说明：多为注释代码，涉及用户信息获取、回调处理等。

---

## @/AppRM 文件夹下 req.session.get 'user' 使用统计（截至2025-07-02）

/**
 * 统计说明：
 * 以下为 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM 及其子目录下，所有通过 req.session.get 'user'（CoffeeScript无括号调用）获取用户信息的代码位置。
 * 每条记录包含文件路径、行号及简要说明。
 * 便于后续代码重构、权限审查及用户信息获取方式统一。
 */

1. user/userPost.coffee
   - 行501: `user = req.session.get 'user'`
   - 行515: `# user = req.session.get 'user'`
   - 说明：发帖相关，获取当前用户信息，部分为注释代码。

2. propRelated/autocomplete.coffee
   - 行199: `user = req.session.get 'user'`
   - 行211: `user = req.session.get 'user'#retObj.user`
   - 说明：自动补全功能，获取当前用户信息。

3. auth/index.coffee
   - 行75: `return resp.redirect '/1.5/user/login' unless user = req.session.get 'user'`
   - 说明：鉴权入口，未登录用户重定向到登录页。

4. 0_common/01_user.coffee
   - 行155: `# if user is req.session.get 'user'`
   - 行168: `# user = req.session.get 'user'`
   - 说明：用户通用逻辑，部分为注释代码。