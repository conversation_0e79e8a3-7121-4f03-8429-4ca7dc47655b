config = CONFIG(['mailEngine','serverBase','contact'])
sendMail = SERVICE 'sendMail'

{sprintf,vsprintf} = INCLUDE 'lib.sprintf'
helpers = INCLUDE 'lib.helpers'
i18n = INCLUDE 'lib.i18n'
libUser = INCLUDE 'libapp.user'
libProperties = INCLUDE 'libapp.properties'
libPropertyImage = INCLUDE 'libapp.propertyImage'

# input: user obj -> eml in string
Properties = MODEL 'Properties'
UserModel = MODEL 'User'
ProjectModel = MODEL 'Project'
BlockPhoneModel = MODEL 'BlockPhone'

ChatDb = COLLECTION 'chome', 'chat'
# User = COLLECTION 'chome', 'user'
#Contacts = COLLECTION 'user_contact'
# UserListings = COLLECTION 'chome', 'user_listing'
Wecard = COLLECTION 'chome', 'wecard'
SignupRecords = COLLECTION 'chome', 'signups'
# debug = console.log

customerServicesStatus = DEF 'customerServicesStatus'
SysNotifyModel = MODEL 'SysNotify'
propTranslate = INCLUDE 'libapp.propertiesTranslate'

htmlToText = sendMail.htmlToText
i18ns = {}
ObjectId = INCLUDE('lib.mongo4').ObjectId
MAX_CHAT_LIMIT = 3500
SOFT_CHAT_LIMIT = 3500
NUM_SESSION_CHAT = 50

userViewStateObj = {} #use id as key
userInChatCountDown = 0
expUsersInChat = ->
  now = Date.now()
  for k,v of userViewStateObj
    if v.exp < now
      delete userViewStateObj[k]
  userInChatCountDown = 0
userInChat = (uid='',chid='')->
  userViewStateObj[uid.toString()] =
    chid: chid.toString()
    exp: Date.now() + 30000
  #debug userViewStateObj
  if userInChatCountDown++ > 1000
    expUsersInChat()
isUserInChat = (uid,chatid)->
  if (u = userViewStateObj[uid.toString()])? and (u.chid is chatid) and (u.exp > Date.now())
    return true
  false

generateMessageBody = (req, from, msg, prop={})->
  text = ''
  if src = req.param 'src'
    text += 'Src: '+src+'<br/>'
  if msg.nm
    text += 'User Name: '+msg.nm+'<br/>'
  if msg.pjnm
    text += 'Project Name: '+msg.pjnm+'<br/>'
  if msg.pjfloorid
    text += 'Project Floor Id: '+msg.pjfloorid+'<br/>'
  # if msg.desc
  #   text += 'Description: '+msg.desc+'<br/>'
  meta = msg.meta or {}
  if meta.title
    text += 'Title: '+meta.title+'<br/>'
  if meta.desc
    text += 'Description: '+meta.desc+'<br/>'
  if msg.eml
    text += 'Email: '+msg.eml+'<br/>'
  if msg.regLang
    text += 'Register Lang:'+msg.regLang+'<br/>'
  if msg.mbl
    text+= 'Mobile: '+msg.mbl+'<br/>'
  if msg.wxid
    text += 'WeChat: '+msg.wxid+'<br/>'
  # if msg.m
  #   fromName = from.split(',')?[0] or from
  #   notifyMsg = 'There is a new message for you from %s. Please login RealMaster APP to reply.'
  #   message = sprintf req.l10n(notifyMsg),fromName
  #   text += 'Message: '+message+'<br/>'
  if msg.sid or msg.rmid
    text += (msg.sid or msg.rmid)+'<br/>'
  if msg.addr
    text += msg.addr + ' ' + (msg.city or '')+'<br/>'
  if msg.url
    text += msg.url+'<br/>'
  if msg.pjnm
    text += 'Proj Id: '+prop._id+'<br/>'
    # TODO: use config.appHost? 这个是忘记了吗？还是没决定好
    text += "Link: <a href='#{req.shareHost()}/projects/details/#{prop._id}'>Detail Link</a>"
  propId = if /^RM/.test(prop.id) then prop.id else prop._id
  # TODO: handle domain
  if (msg.sid or msg.rmid or msg.id) and prop._id
    text += 'Prop Id: '+propId+'<br/>'
    # TODO: use config.appHost?
    text += "Link: <a href='#{req.shareHost()}/1.5/prop/detail?inframe=1&id=#{propId}'>Detail Link</a><br/>"
  if prop.addr
    text += 'Details: '+prop.addr+', '+prop.city+', '+prop.prov+'<br/>'
  m = if text then (": <br/>" + text) else ''
  notReplyMsg = 'Please reply in RealMaster APP chat. Sender may not receive your reply here.'
  if msg._ and 'function' is typeof msg._
    notReplyMsg = msg._ notReplyMsg
  else
    notReplyMsg = req.l10n notReplyMsg
  m = '<div style="font-size:12px;color:red;">'+notReplyMsg+'</div><br/>'+m
  m

push_notify_one = (req,chatid,owner,msg,from,replyTo,prop)->
  return if isUserInChat owner._id,chatid
  lang = owner.locale or 'en'
  _ = (i18ns[lang] ?= i18n.getFun(lang))
  opt =
    from: _ "RealMaster"
    replyTo: replyTo
    subject: sprintf _("You got new message from %s"),from
    pn:1
    mail:1
    url: '/chat/list'
    engine: config.mailEngine?.mailEngine or 'SES' #'sendmail' #'gmail'
  #  sms:1 # user.mbl is not saved in list
  # debug owner
  if req.isAllowed 'vipUser', owner
    opt.sms = 1
    owner.mbl = ''+owner.mbl if owner.mbl
  msg._ = _
  m = generateMessageBody(req, from, msg, prop)
  # console.log '+++++'
  # console.log opt
  # console.log owner
  # console.log m
  # return
  SysNotifyModel.sendToUser owner,opt,m,(err)->


push_notify_all_users = (req,owner,chatid,usrs, msg, prop)->
  return null unless usrs
  eml = UserModel.getEmail owner
  userIdArr = []
  for u in usrs
    userIdArr.push u._id
  UserModel.getListByIds userIdArr,{
    projection:{_id:1, pn:1, mbl:1, eml:1, roles:1, locale:1},
    isDeveloperMode:config.serverBase?.developer_mode
  }, (err, ret)->
    console.error err if err
    ret ?= []
    # idPnMap = {}
    # for u in ret
    #   idPnMap[u._id] = u.pn
    for u in ret
      # if pn = idPnMap[u._id]
      #   u.pn = pn
      if (UserModel.getEmail(u) isnt eml)
        fromUserDetail = libUser.fullNameOrNickname(req.locale(),owner)+', '+UserModel.getEmail(owner)
        push_notify_one req,chatid,u,msg,fromUserDetail,eml,prop

getLvts = (eml, c)->
  return c.lvts if c.lvts
  for u in c.usrs
    if eml is u.eml
      #debug 'found'
      return u.lvts if u.lvts
  if c.msgs?.length #no user lvts and has msgs
    return true
  return new Date()


# return if this user has new msgs
getHasNewMsgs = (req, cb)->
  try
    user = await UserModel.getFromSession {req, userfields: ['eml']}
  catch err
    return cb false
  unless eml = UserModel.getEmail user
    return cb false
  if (hasNewMsg = req.session.get('hasNewMsg'))?
    return cb hasNewMsg
  q = {'usrs.eml':eml}
  ChatDb.findToArray q, (err, ret)->
    return cb false if err
    hasNewMsg = 0
    for c in ret
      lvts = getLvts(eml, c)
      if c?.lm?.ts > lvts
        hasNewMsg += 1
        # hasNewMsg = true
        # break
    # save to session, so we don't hit db so often, espacially when use remote db and homepage
    req.session.set 'hasNewMsg',hasNewMsg,->
      cb hasNewMsg
DEF 'getHasNewMsgs', getHasNewMsgs

APP 'chat'
system_user = {
  eml:config.contact?.defaultRTEmail
  pn:null
  _id:'RealMaster-Team' #TODO: search for this user in Users
}
# get all chat conversations for user,
# at least user.eml is needed
get_all_dialogs = (user, cb)->
  eml = UserModel.getEmail(user)
  q = {'usrs.eml':eml}
  opt =
    sort:[['mt','descending']] # shall show in newest first order
    fields:{msgs:0} # db may be remote, get only fields needed
  ChatDb.findToArray q,opt,(err, ret)->
    return cb err, ret

# get all user's contacts
get_all_contacts = (user, cb)->
  ret = []
  return cb ret

is_allowed_to_chat = (user)->
  return true unless user?.roles #no roles user
  if user.roles.indexOf('realtor') < 0
    return true
  for role in user.roles
    if role in ['vip', 'vip_plus', 'tp']
      return true
  return false

# internal api create chat from user to who
# user.eml is required, could be from outside
# who is app user, have eml
# will create default chat from ctx
# follow: 我是fn ln, nm
propFields = ['lp','lpr', 'addr','bdrms','tbdrms','kch','sid','stp','ptp','pstyl', \
'ptype2', 'bdrms','lp_price','city','prov','lpunt', 'pho', 'saletp','nm','ptype','m']
create_user_chat = (req, user, who, ctx, cb)->
  # return cb 'Error cant set up chat'
  oid = user._id.toString()
  usr1 =
    eml:UserModel.getEmail(user)
    pn: user.pn
    _id: oid
  ctx.f ?= 0
  construct_msg req, user, ctx, (initMsg)->
    vals = {
      usrs:[usr1, who]
      msgs:[]
      ts:new Date()
      lm:{}
    }
    if ctx.rmid or ctx.sid or ctx.pjid or ctx._id
      vals.msgs = [initMsg]
      vals.lm = initMsg
    if ctx.wpid
      vals.tl   = ctx.meta?.title
      vals.desc = ctx.meta?.desc
      vals.img  = ctx.meta?.img
    u1 = {oid:oid, eml:who.eml, ts:new Date(), uid:who._id}
    u2 = {oid:who._id, eml:UserModel.getEmail(user), ts:new Date(), uid:oid}
    # do not use contacts table anymore
    # update_contacts u1, u2, (err, ret)->
    #   return cb err if err
    # console.log '+++++usr1',usr1,'++++who',who
    ChatDb.insertOne vals, (err, ret)->
      return cb err if err
      #TODO: created chat, notify who, use who.pn
      # _id was added to vals after insert
      vals._id ?= ret.insertedId
      return cb null, vals

# Don't use contacts any more
# # create one with ts
# create_contacts = (u, cb)->
#   u.ts = new Date()
#   Contacts.insertOne u, (err, ret)->
#     return cb err if err
#     return cb null, ret
#
# # update one contact, if not exists create
# update_contact = (u, cb)->
#   q = {oid:u.oid, eml:u.eml}
#   Contacts.findOneAndUpdate q, {$set:u}, (err, ret)->
#     return cb err if err
#     unless ret?.value
#       create_contacts u, (err, ret)->
#         return cb err if err
#         return cb null, ret
#     else
#       cb null, ret
#
# # mainly update ts
# update_contacts = (u1, u2, cb)->
#   update_contact u1, (err, ret)->
#     return cb err if err
#     update_contact u2, (err, ret)->
#       return cb err if err
#       return cb null, ret


# get user formated name
# TODO: if none, use eml
get_user_fnm = (req,user)->
  if name = libUser.fullNameOrNickname req.locale(),user
    return name
  else
    return user.nm or user.eml

is_valid_id = (id)->
  /^\w{24}$/i.test(id)

# translate prop and return new prop with selected fields
get_translated_prop_fields = (req, prop)->
  req.setupL10n() unless req._
  # translateRmPropDetailOpt = {_:req._,_ab:req._ab,l10n:req.l10n,locale:req.locale,headers:req.headers,cookies:req.cookies}
  prop = propTranslate.translate_rmprop_detail({locale:req.locale()}, prop)
  nprop = {}
  for f in propFields
    nprop[f] = prop[f]
  nprop

format_price = (n)->
  n2 = parseFloat n
  return n if isNaN n2
  n2.toFixed(2).replace(/(\d)(?=(\d{3})+\.)/g, '$1,')

# if sending a listing, include listing detail in msg
get_chat_listing_detail = (req, id, cb)->
  Properties.findOneByFuzzID id,{projection:propFields},(err,prop)->
    if err
      console.error err
      return cb err
    prop ?= {}
    prop.lp_price = format_price(prop.lp)
    cb null,prop



# construct  msg
construct_msg = (req, user, ctx, cb) ->
  msg =
    f:ctx.f or 0
    ts: ctx.ts or new Date()
  if ctx.sid or ctx.rmid or ctx._id
    if ctx._id
      id = ctx._id
    else if ctx.rmid
      id = ctx.rmid
    else
      id = 'TRB' + ctx.sid #TODO: ddf?
    msg.url = ctx.url
    unless msg.url
      msg.url = req.shareHost()+'/1.5/prop/detail/inapp?nobar=1&id='+id
      # if id = ctx.rmid
      #   msg.url = req.shareHost()+'/1.5/prop/detail?nobar=1&id='+id
      # else if id= ctx.sid
      #   msg.url = req.shareHost()+'/1.5/prop/detail?nobar=1&sid='+id
      #   msg.url = "/1.5/rmprop/detail/"+ctx.rmid
      # else
    if ctx.rmid
      msg.rmid = ctx.rmid
    msg.tp = ctx.type or 'lst'
    msg.m = ctx.m or ''
    msg.img = ctx.img or '/img/noPic.png'
    get_chat_listing_detail req, id, (err, ret)->
      if err
        msg.tp = 'text'
        msg.m = id + '--' + (msg.m or '')
        return cb msg
      for k,v of ret
        if (k is 'm') and v.length > 100
          v = v.substring(0, 100)
          v += '...'
        if (k is 'ts')
          v = new Date()
        msg[k] = v
      return cb msg
  else if ctx.pjid
    msg.url = req.shareHost()+'/1.5/prop/projects/detail?nobar=1&id='+ctx.pjid
    msg.tp = ctx.type or 'project'
    msg.m = ctx.m or ''
    msg.img = ctx.img or '/img/noPic.png'
    msg.pjnm = ctx.pjnm
    msg.addr = ctx.addr
    msg.city = ctx.city
    msg.prov = ctx.prov
    msg.noNotify = true
    return cb msg
  else if ctx.wpid
    msg.tl = ctx.meta?.title
    msg.desc = ctx.meta?.desc
    msg.tp = ctx.type or 'cmt'
    msg.url = ctx.url
    msg.m = ctx.m or ''
    msg.img = ctx.img or '/img/noPic.png'
    return cb msg
  else
    msg.m = ctx.m or req.l10n("Hello, I'm ") + get_user_fnm(req, user)
    msg.tp = ctx.tp or 'text'
    return cb msg

# grp = {
#  type: 'listing'
# }
create_grp_chat = (user, grp, cb)->
  ret = {ok:1}
  return cb ret

# find chat of this user and who,
# return _id, if not exists create_user_chat
find_user_chat = (req, user, who, opts, cb)->
  eml = UserModel.getEmail(user)
  q = {'usrs.eml':eml, 'usrs._id': who._id, usrs:{$size:2}}
  update = {mt:new Date()}
  ChatDb.findToArray q, {msgs: { $slice:[ opts.skip, opts.limit ]}}, {upsert:false}, (err, retList)->
    return cb err if err
    idx = 0
    if retList.length > 1
      errMsg = "Error: only one user chat is suppoted, has many: #{eml}, users._id:#{who._id}"
      req.logger?.error errMsg
      console.error errMsg
      if chatid = req.param 'chatid'
        for c,i in retList
          if c._id.toString() is chatid
            idx = i
            break
    ret = retList[idx]
    if ret?.msgs
      return cb null, ret
    opts.f ?= 0
    # console.log 'user: ', user
    # console.log 'who: ',who
    create_user_chat req, user, who, opts, (err, chat)->
      return cb err if err
      chat.newChat = 1
      return cb null, chat

# given chatid push to chat,
# TODO: check if user in usrs
# TODO: decide message.f at server
push_to_chat = (req, chatid, user, msg, prop, cb)->
  meta = null
  if msg.meta
    meta = msg.meta
    delete msg.meta
  push =
    msgs: { $each: [msg], $slice: - MAX_CHAT_LIMIT }
  set =
    lm:msg
    mt:new Date()
  ChatDb.findOneAndUpdate {_id: chatid}, {$push:push, $set:set}, {returnDocument:'after'}, (err, ret)->
    return cb(err) if err
    msg.meta = meta
    push_notify_all_users req,user,chatid,ret?.value?.usrs,msg,prop unless msg.noNotify
    update_lvts chatid, UserModel.getEmail(user) unless msg.nm or msg.eml #from signup, not update lvts
    return cb null, ret?.value?.msgs?.length

dataMethods = DEF 'dataMethods'

dataMethods.chatList = (req, user, cb)->
  cb null, {l:[], usrs:{}} unless user
  if (uid = req.body.uid) and req.isAllowed 'productSales'
    UserModel.findById uid,{projection:{eml:1}},(err, user) ->
      return cb err if err
      return getChatList req, user, cb
  else
    getChatList(req, user, cb)

getChatList = (req, user, cb)->
  retl = []
  usrs = {}
  allUsers = []
  done = ()->
    retl.sort((a,b)->
      return b.mt-a.mt
    )
    cb null, {l:retl, usrs:usrs}
  get_all_dialogs user, (err, ret)->
    return done() if err
    for i in ret
      i.lm ?= {}
      if Object.keys(i.lm)?.length is 0
        # console.error 'Error: Chat: _id: '+i._id+' -> No messages.'
        continue
      delete i.msgs
      i.usrsCount = i.usrs.length
      if i.usrsCount is 2
        i.uid = get_another_uid(user, i.usrs)
      allUsers = allUsers.concat(i.usrs)
      i.emls = (u.eml for u in i.usrs)
      [i.me, me] = get_my_chat_index(UserModel.getEmail(user), i.usrs)
      i.lvts = i.lvts if i.lvts# sysmsg in i.lvts
      i.lvts ?= me.lvts
      i.mt ?= i.ts
      delete i.usrs
      retl.push i
    set_up_user_fns allUsers, (err, ret2)->
      return done() if err
      for u in ret2
        usrs[u.eml] = u or {}
      req.session.set('hasNewMsg', null) #user clicked list
      return done()

# show all chats
GET 'list', (req, resp) ->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->
    resp.ckup 'dialogList', {}, '_', {noAngular:1,noRatchetJs:1,noUserModal:1}


_doSysChatRet = (req, ret, cb)=>
  if ret.prov and /\w+/.test  ret.prov
    prop = {}
    for i in propFields
      prop[i] = ret[i]
    req.setupL10n() unless req._
    # translateRmPropDetailOpt = {_:req._,_ab:req._ab,l10n:req.l10n,locale:req.locale,headers:req.headers,cookies:req.cookies}
    prop = propTranslate.translate_rmprop_detail({locale:req.locale()}, prop)
    for i in propFields
      ret[i] = prop[i]
  cb null, ret

#should add chat auth check,
dataMethods.sysChat = (req, user, cb)->
  return cb null,{} unless chatid = req.body.chatid
  if (selfuid = req.param('selfuid')) and req.isAllowed 'productSales'
    ChatDb.findOne {_id:chatid}, (err, ret)->
      return cb(err) if err
      return cb('Chat not found') unless ret
      return _doSysChatRet req, ret, cb
  else
    set = {lvts:new Date()}
    ChatDb.findOneAndUpdate {_id:chatid},{$set:set}, (err, ret)->
      return cb(err) if err
      return cb('Chat not found') unless ret?.value
      return _doSysChatRet req, ret.value, cb

# show all chats
GET 'w/:id', (req, resp) ->
  findErr = (err) ->
    resp.ckup 'generalError', {err:err.toString()}
  UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->
    chatid = req.param 'id'
    selfuid = req.param 'selfuid'
    return findErr 'no id' unless chatid
    resp.ckup 'sysChat', {chatid:chatid, selfuid:selfuid}


#open single chat
GET 'u/:uid', (req, resp) ->
  # TODO: check if isRealtor and not vip show error msg, auth
  findErr = (err) ->
    resp.ckup 'generalError', {err:err}
  # unless req.getDevType() is 'app'
  #   return findErr('In app only')
  UserModel.appAuth {req,resp,url:'/1.5/user/login',userfields:['eml','pn','nm_zh','nm_en','nm']}, (user) ->
    if (selfuid = req.param('selfuid')) and req.isAllowed 'productSales'
      UserModel.findById selfuid,{projection:{eml:1,pn:1,locale:1}},(err, _user)->
        return findErr err if err
        return findErr "no user found" if not _user
        return getSingleChat(req, resp, _user)
    else
      return getSingleChat(req, resp, user)

getSingleChat = (req, resp, user)->
  findErr = (err) ->
    resp.ckup 'generalError', {err:err}
  uid = req.param 'uid'
  return findErr('No id') unless uid
  isSysUser = uid is system_user._id
  return findErr('Not valid uid') unless is_valid_id(uid) or isSysUser
  myeml  = UserModel.getEmail(user)
  sid    = req.param 'sid'
  rmid   = req.param 'rmid'
  _id    = req.param '_id'
  url    = req.param 'url'
  pjid = req.param 'pjid'
  img    = req.param('img')
  img = decodeURIComponent(img) if img
  img = null if img is 'null'
  isNew = req.param 'new'
  done = (ret)->
    cfg    = {}
    cfg.chatid = ret._id
    cfg.selfuid = req.param 'selfuid'
    cfg.msgs   = ret.msgs
    cfg.me     = get_my_chat_index(myeml, ret.usrs)[0]
    set_up_user_fns ret.usrs, (err, usrs)->
      return findErr(err) if err
      cfg.usrs = usrs
      #setup global object
      userInChat user._id,ret._id
      resp.ckup 'chat', cfg, '_', {noAngular:1,noRatchetJs:1,noUserModal:1}
  UserModel.findById uid,{projection:{eml:1,pn:1,locale:1}}, (err, retusr)->
    return findErr(err) if err
    if not retusr
      return findErr 'User Not found'
    if UserModel.getEmail(retusr) is myeml
      return findErr 'Cannot chat to yourself'
    opts =
      skip:   - NUM_SESSION_CHAT
      limit:  NUM_SESSION_CHAT
      sid: sid
      rmid:   rmid
      _id: _id
      pjid: pjid
      url:    url
      type:   req.param 'type'
      img:    img
      pjnm: req.param 'pjnm'
      addr: req.param 'addr'
      city: req.param 'city'
      prov: req.param 'prov'
    retusr.eml = UserModel.getEmail(retusr)
    retusr._id = retusr._id.toString()
    find_user_chat req, user, retusr, opts, (err, ret)->
      return findErr(err) if err
      #insert new listing msg if chat already exists, newChat set in find_user_chat
      # console.log (opts._id or opts.sid or opts.rmid or opts.pjid),isNew,ret
      if (opts._id or opts.sid or opts.rmid or opts.pjid) and isNew and (not ret.newChat)
        construct_msg req, user, opts, (newMsg)->
          push_to_chat req, ret._id.toString(), user, newMsg, {}, (err, length)->
            return findErr(err.toString()) if err
            ret.msgs.push newMsg
            ret.lm = newMsg
            return done(ret)
      else
        return done(ret)

# find user in [emls] write back to object in list
set_up_user_fns = (usrs, cb)->
  ul = []
  emlMapping = {}
  for u in usrs
    u.eml = UserModel.getEmail(u)
    ul.push u.eml
    emlMapping[u.eml] = {}
  fields = {eml:1, fn:1, ln:1, avt:1, nm:1, locale:1, nm_zh:1, nm_en:1}
  UserModel.getListByEmls ul, {projection:fields}, (err, uinfos) ->
    return cb(err) if err
    # set mapping
    for u in uinfos
      u.eml = UserModel.getEmail(u)
      u.avt = libPropertyImage.replaceRM2REImagePath u.avt if u.avt
      emlMapping[u.eml] = u
    # set values in usrs
    for u in usrs
      delete u.pn
      for k,v of fields
        if emlMapping[u.eml]?[k]
          u[k] = emlMapping[u.eml][k]
    cb null, usrs


# delete pn from usrs
filter_user_info = (usrs)->
  return usrs unless usrs?.length
  for u in usrs
    delete u.pn
  usrs

# get user index in usrs
get_my_chat_index = (myeml, usrs) ->
  for u, idx in usrs
    if u.eml is myeml
      return [idx, u]
  return [0,{}]

# for 2 chat get another user _uid
get_another_uid = (user, usrs) ->
  return null unless usrs
  eml = UserModel.getEmail(user)
  for u in usrs
    if u.eml isnt eml
      return u._id
  null

# group chat entrance
GET 'g/:grpid', (req, resp) ->
  resp.send 'under const'

#chatlist view
VIEW 'dialogList', ->
  div id:'vueBody',->
    text """<chatlist></chatlist>"""
  js '/js/entry/commons.js'
  js '/js/entry/chatlist.js'

#chat entrance view
VIEW 'chat', ->
  coffeejs {vars:{
    chatid:@chatid,
    selfuid: @selfuid,
    me:@me,
    msgs:@msgs,
    usrs:@usrs}}, ->
    null
  div id:'vueBody',->
    text """<chat></chat>"""
  js '/js/entry/commons.js'
  js '/js/entry/chat.js'

#system message entry
VIEW 'sysChat', ->
  coffeejs {vars:{
    chatid:@chatid,
    selfuid: @selfuid,
    }}, ->
    null
  div id:'vueBody',->
    text """<syschat></syschat>"""
  js '/js/entry/commons.js'
  js '/js/entry/syschat.js'



APP 'api', true

# requestchat in leads and wechat, not allowed to chat to leads if not vip
POST 'requestchat', (req, resp)->
  # return resp.send {ok:0}
  UserModel.appAuth {req,resp}, (user) ->
    findErr = (err)->
      resp.send {ok:0, err:err}
    unless user
      return findErr('Not loged in')
    unless is_allowed_to_chat(user)
      return findErr('Need Vip')
    resp.send {ok:1}

# not used
POST 'getchat', (req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    findErr = (err)->
      resp.send {ok:0, err:req.l10n(err)}
    unless user
      return findErr('Not loged in')
    unless is_allowed_to_chat(user)
      return findErr('You have to be vip to use this')
    chatid = req.param 'chatid'
    return findErr('no chat id') unless chatid
    return findErr('Not valid chat id') unless is_valid_id chatid
    skip  = parseInt(req.param 'skip' or -50)
    limit = parseInt(req.param 'limit' or 50) # last 50 records
    ChatDb.findOne {_id:chatid}, { msgs: { $slice: [ skip, limit ]}}, (err, ret)->
      return findErr('chat not found') unless ret
      filter_user_info(ret.usrs)
      resp.send {ok:1, msgs:ret.msgs, usrs:ret.usrs}

# give uid find user
# find_user_by_uid (uid, cb)->
#   UserModel.findById uid,{},(err, user)->
#     return cb err if err
#     return cb err unless user
#     cb null, user

# find_user_by_eml = (eml, cb)->
#   UserModel.findByEml eml,{},(err, user)->
#     return cb err if err
#     return cb('User Not found: '+eml) unless user
#     cb null, user

# given id update lvts for user
update_lvts = (chatid, eml, cb)->
  q = {_id:chatid, "usrs.eml" : eml}
  set = {"usrs.$.lvts":new Date()}
  ChatDb.updateOne q, {$set:set}, (err, ret)->
    return cb err if cb and err
    # console.log ret?.result?.nModified

#give wpid or rmid find user
find_user_by_wpid_or_rmid = (req, opt, cb)->
  if opt.wpid
    Wecard.findOne {_id:opt.wpid}, {meta:1, uid:1}, (err, wcard)->
      return cb err if err
      return cb 'No wecard found' unless wcard
      UserModel.findById wcard.uid.toString(),{}, (err, user)->
        return cb err if err
        return cb 'Error user not found' unless user
        user.meta = wcard.meta
        return cb null, user
  #if has ueml is report error
  else if opt.rmid and not opt.ueml
    # UserListings.findOne {id:opt.rmid}, (err, prop)->
    Properties.findUIDByID opt.rmid, (err,prop)->
      return cb err if err
      return cb 'No listing found' unless prop
      #TODO: do we find which user? original poster or the one who shared listing?
      UserModel.findById prop.uid.toString(),{}, (err, user)->
        return cb err if err
        return cb 'Error user not found' unless user
        user.prop = prop
        return cb null, user
  else if opt.pjid
    ProjectModel.findDetail {_id:opt.pjid}, (err, prop)->
      return cb err if err
      return cb 'No listing found' unless prop?.uid
      uid = prop.uid.toString()
      emailList = prop.sendToEmails or ''
      emailList = emailList.split(',') or []
      if req.param 'uid'
        uid = req.param 'uid'
      UserModel.findById uid,{},(err, user)->
        return cb err if err
        return cb 'Error user not found' unless user
        user.prop = prop
        user.emailList = emailList
        return cb null, user
  else if opt.uid
    UserModel.findById opt.uid,{}, (err, user)->
      return cb err if err
      return cb 'Error user not found' unless user
      if opt.sid
        return get_chat_listing_detail req, opt.sid, (err, ret)->
          return cb err if err
          user.prop = ret
          return cb null, user
      return cb null, user
  else if opt.ueml
    UserModel.findByEml opt.ueml,{}, (err, user)->
      return cb err if err
      return cb 'Error user not found' unless user
      tmpid = opt.sid or opt.rmid
      if tmpid
        return get_chat_listing_detail req, tmpid, (err, ret)->
          return cb err if err
          user.prop = ret
          return cb null, user
      return cb null, user
  else
    return cb 'Not supported'

sendEmail = (to, body, from, opt={})->
  textbody = htmlToText.fromString(body, {})
  engine = opt.engine or 'SES'
  mail =
    _trans: 'smtp'
    engine: engine # 'gmail'
    from: "#{from or ''}<#{sendMail.getFromEmailByEngine(engine)}>"
    replyTo: opt.replyTo or "RealMaster Technology Inc.<#{config.contact?.defaultRTEmail}>"
    to: to
    subject: opt.subject or 'New Message From RealMaster APP'
    text: textbody
    html: body
    isNeedValidate: true
    eventType: 'ChatEmail'
  # console.log '++++'
  # console.log mail
  # return
  sendMail.sendMail mail,(err,response)->
    if err
      console.error err

#proj has new feedback, email all subscribed admins(<EMAIL>/creator)
notify_email_subscribers = (req, emls, opt, msgObj, from)->
  m = generateMessageBody(req, from, msgObj, opt.prop)
  for eml in emls
    unless helpers.isEmail(eml)
      continue
    # if opt.noSendInfo and eml is appConfig.defaultEmail
    #   continue
    sendEmail eml, m, from, opt
  null

adminObserveSignupMessage = (req, user, opt)->
  proj = user.prop
  return unless proj
  toUserUid = user._id
  toUserEml = user.eml.toString()
  paramUid = req.param 'uid'
  projUid = proj.uid.toString()
  record = {
    toUserUid,
    toUserEml,
    paramUid,
    projUid,
    ts: new Date()
    emls: proj.sendToEmails
    projNm:proj.nm
    eml:opt.eml
    mbl:opt.mbl
    nm:opt.nm
    m:opt.m
    img:opt.img
    url:opt.url
    ueml:opt.ueml
    pjfloorid:opt.pjfloorid
  }
  SignupRecords.insertOne record,(err,ret)->
    console.error err if err
    console.log 'MSG: user signup recorded'

# TODO: split into new funs
isValidFeedback = (opt)->
  if opt.ueml and opt.src is 'renovation'
    return true
  return opt.wpid or opt.sid or opt.rmid or opt.pjid

POST 'feedback', (req, resp)->
  findErr = (err,t)->
    ret = {ok:0, err:err}
    ret.err = req.l10n(err.toString()) if t
    resp.send ret
  opt =
    sys:1 #send initial msg, do not reply
    f:0
    skip:   - NUM_SESSION_CHAT
    limit:  NUM_SESSION_CHAT
  for i in ['wpid','sid','rmid','eml','mbl','wxid','nm','m','uid','img','url','pjid','ueml','pjfloorid','src']
    if req.body[i] and ('string' is typeof req.body[i])
      opt[i] = helpers.htmlEscape req.body[i]
    else
      opt[i] = req.body[i]
  unless isValidFeedback(opt)
    return findErr('Not Valid Data',1)
  # unless opt.wxid
  #   return findErr('No user data',1)
  if opt.sid and (not opt.uid and not opt.ueml)
    return findErr('No owner info',1)
  opt.type = if opt.wpid then 'wcd' else 'lst'
  UserModel.appAuth {req,resp,userfields:['mbl','pn']}, (user) ->
    if opt.mbl and not user?.mbl
      UserModel.updateMobel {req,mbl:opt.mbl},(err,ret)->
        console.error err if err
        # console.log 'MSG: updated user mbl'
    find_user_by_wpid_or_rmid req, opt, (err, user)->
      return findErr(err) if err
      opt.meta = user.meta #if wpid
      opt.prop = user.prop #if rmid/pjid
      adminObserveSignupMessage(req, user, opt) if opt.pjid
      find_sys_chat req, user, opt, (err, chat)->
        return findErr(err) if err
        #setup msg
        msg = {ts:new Date()}
        usrFlds = ['nm','eml','wxid','mbl','m','meta']
        for f in usrFlds
          if opt[f]
            msg[f] = opt[f]
        if prop = user.prop
          msg.pjnm = prop.nm if prop.nm
          msg.desc = prop.desc if  prop.desc
          msg.pjfloorid = prop.pjfloorid if prop.pjfloorid

        from = {eml:opt.eml,nm:opt.nm}
        msg.regLang = req.locale()
        if Array.isArray(user?.emailList) and not req.param 'noInform'
          fromStr = opt.eml or opt.nm
          notify_email_subscribers(req, user.emailList, opt, msg, fromStr)
        push_to_chat req, chat._id.toString(), from, msg, opt.prop, (err, length)->
          return findErr(err) if err
          msg = req.l10n('Your request has been submitted!')
          if not customerServicesStatus.list.info
            msg += ' '
            msg += req.l10n('Supporting staff is offline now, we will contact you ASAP or on next business day.')
          resp.send {ok:1, msg:msg}

# find system chat
find_sys_chat = (req, user, opts, cb)->
  eml = UserModel.getEmail(user)
  q = {'usrs.eml':eml, sys:1, usrs:{$size:1}}
  update = {mt:new Date()}
  q.sid = opts.sid if opts.sid
  q.wpid = opts.wpid if opts.wpid
  q.rmid = opts.rmid if opts.rmid
  q.pjid = opts.pjid if opts.pjid
  q.src = opts.src if opts.src is 'renovation'
  # console.log '+++++'
  # console.log opts
  ChatDb.findOne q, {msgs: { $slice:[ opts.skip, opts.limit ]}}, {upsert:false}, (err, ret)->
    return cb err if err
    unless ret
      create_sys_chat req, user, opts, (err, chat)->
        return cb err if err
        return cb null, chat
      return
    cb null, ret

#create a system chat
create_sys_chat = (req, user, opts, cb)->
  # return cb 'Error cant set up chat'
  oid = user._id.toString()
  usr1 =
    eml:UserModel.getEmail(user)
    pn: user.pn
    _id: oid
  vals = {
    usrs:[usr1]
    msgs:[]
    ts:new Date()
    lm:null
    sys:1
    lvts: new Date()
  }
  for i in ['wpid','sid','rmid','url','img','pjid','pjfloorid','src']
    vals[i] = opts[i] if opts[i]
  if opts.rmid or opts.sid or opts.pjid
    for i in propFields
      vals[i] = opts.prop[i] if opts.prop and opts.prop[i] #could be 0, do not use opts?.prop => null
  if opts.wpid
    vals.tl   = opts.meta?.title
    vals.desc = opts.meta?.desc
    vals.img  = opts.meta?.img
  ChatDb.insertOne vals, (err, ret)->
    return cb err if err
    return cb null, vals

# given chatid get new msgs after given ts
# TODO: check usr in usrs
POST 'getmsgs', (req, resp)->
  UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->
    findErr = (err)->
      resp.send {ok:0, err:req.l10n(err)}
    unless user
      return findErr('Not loged in')
    chatid = req.param 'chatid'
    return findErr('no chat id') unless chatid
    return findErr('Not valid chat id') unless is_valid_id chatid
    older = req.param('older')
    ts = new Date(req.param('ts'))
    return findErr('no ts') unless ts
    # find msgs whitch ts > user last message ts
    # TODO: updte global object
    cond =
      $gt: ['$$msg.ts', ts]
    if older #show older msgs
      cond =
        $lt: ['$$msg.ts', ts]
    filter = {
      $filter:
        input: '$msgs'
        as: 'msg'
        cond:cond
    }
    # TODO: https://docs.mongodb.com/manual/reference/operator/aggregation-pipeline/
    # When performance is a issue
    # could use $unwind/$sort/$limit/$group to limit to last n records.
    # Or $slice and $filter and be pipelined
    #
    # $slice: [ "$msgs",  NUM_SESSION_CHAT ]
    #If negative, $slice determines the starting position from the end of the array.
    #If the absolute value of the <position> is greater than the number of elements,
    #the starting position is the start of the array.
    q = [
      { $match: _id: new ObjectId(chatid) }
      { $project:{
          num: { $size: '$msgs' },
          msgs:filter
          # msgs2: {$slice: [ "$msgs", - NUM_SESSION_CHAT ]}
        }
      },
      { $project:{
          num: { $size: '$msgs' },
          msgs:{$slice: [ "$msgs", - NUM_SESSION_CHAT ]}
        }
      }
    ]
    ChatDb.aggregate  q, {cursor:{batchSize:0}},(err, ret)->
      ret = ret?[0]
      return findErr('chat not found') unless ret
      if older and ret.msgs.length
        ret.msgs = ret.msgs.splice(-NUM_SESSION_CHAT)
      update_lvts chatid, UserModel.getEmail(user)
      userInChat user._id,chatid
      resp.send {ok:1, msgs:ret.msgs}

MSG_STRINGS.def {
  REJECTD_MSG:'Message rejected by the receiver.'
}
# user send a chat msg
POST 'sendchat', (req, resp)->
  UserModel.appAuth {req,resp,userfields:['blkCht','pn','devId']}, (user) ->
    findErr = (err,doTrans)->
      ret = {ok:0, err:err}
      if doTrans
        ret.err = req.l10n(err)
      resp.send ret
    unless user
      return findErr(MSG_STRINGS.NEED_LOGIN,1)
    msg = req.param 'msg'
    unless msg
      return findErr(MSG_STRINGS.BAD_PARAMETER,1)
    chatid = req.param 'chatid'
    return findErr(MSG_STRINGS.BAD_PARAMETER,1) unless chatid
    return findErr(MSG_STRINGS.BAD_PARAMETER,1) unless is_valid_id chatid
    isRealtor = UserModel.isRealtor user
    isVip = UserModel.isVip user
    try
      verified = await UserModel.checkVerify user._id
      isPhoneNumberBlocked = await BlockPhoneModel.isPhoneNumberBlocked verified.mbl if verified.mbl
    catch err
      console.error err
      return findErr(MSG_STRINGS.DB_ERROR)
    if (not verified?.emlV) and (not (isRealtor or isVip))
      return  findErr(MSG_STRINGS.EMAIL_NOT_VERIFIED,1)
    if verified.mbl and isPhoneNumberBlocked
      return findErr(req.l10n(MSG_STRINGS.PHONE_BLOCKED))
    UserModel.isChatBlocked {user}, (ret) ->
      blocked = ret[0]
      byWhich = ret[1]
      # console.log '++++++-----chatBLocked',user,ret
      if blocked
        return resp.send {ok:0,err:req.l10n(MSG_STRINGS.REJECTD_MSG)}
      ret = {ok:1}
      tp  = req.param 'tp'
      f = parseInt(req.param('f') or 0)
      # TODO: construct_msg req, user, ctx, (initMsg)-> to support user send listing
      msg =
        m:msg
        f:f
        ts: new Date()
        tp:tp or 'text'
      push_to_chat req, chatid, user, msg, {}, (err, length)->
        return findErr(err.toString()) if err
        ret.msg = [msg]
        if length > SOFT_CHAT_LIMIT #and not req.session.get('max_chat_limit_notification_send')
          #TODO: refer: 相对refer, 在此对话前n条
          # req.session.set 'max_chat_limit_notification_send', 1, ()->
          ret.msg.push {
            tp: 'util'
            m:  req.l10n 'Reaching chat limit, previous chat will be removed from server'
            ts: new Date()
          }
        resp.send ret


# user send a chat msg
POST 'delete', (req, resp)->
  UserModel.appAuth {req,resp}, (user) ->
    findErr = (err,t)->
      ret = {ok:0, err:err}
      if t
        ret.err = req.l10n(err)
      resp.send ret
    unless user
      return findErr('Not loged in',1)
    chatid = req.param 'chatid'
    unless chatid
      return findErr('No chatid',1)
    return findErr('Not valid chat id',1) unless is_valid_id chatid
    ret = {ok:1}
    ChatDb.findOne {_id:chatid},(err,ret)->
      return findErr(err,toString()) if err
      return findErr('Chat not found',1) unless ret
      return findErr('Unable to delete',1) unless ret.sys
      ChatDb.deleteOne {_id:chatid}, (err, ret)->
        return findErr(err,toString()) if err
        resp.send ret
